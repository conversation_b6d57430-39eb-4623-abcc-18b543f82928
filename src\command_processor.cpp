#include "command_processor.h"
#include "usb_manager.h"
#include "led_controller.h"

// External USB Serial reference
extern USBCDC USBSerial;
#include "system_info.h"

// Global instance
CommandProcessor commandProcessor;

CommandProcessor::CommandProcessor() 
    : commandQueue(nullptr), commandBuffer(""), commandsProcessed(0), lastHeartbeat(0) {
}

void CommandProcessor::begin() {
    // Create command queue
    commandQueue = xQueueCreate(QUEUE_SIZE, sizeof(HIDCommand));
    
    if (commandQueue == NULL) {
        usbManager.sendDebugMessage("ERROR: Failed to create command queue");
        ledController.setState(LED_ERROR);
        return;
    }
    
    lastHeartbeat = millis();
    usbManager.sendDebugMessage("Command processor initialized");
}

void CommandProcessor::processSerialCommands() {
    // OPTIMIZATION: Process multiple commands in one call to handle bursts
    int commandsProcessedThisCall = 0;
    const int MAX_COMMANDS_PER_CALL = 10; // Limit to prevent blocking

    while (USBSerial.available() && commandsProcessedThisCall < MAX_COMMANDS_PER_CALL) {
        char c = USBSerial.read();

        if (c == '\n' || c == '\r') {
            if (commandBuffer.length() > 0) {
                if (parseCommand(commandBuffer)) {
                    ledController.setState(LED_COMMAND_RECEIVED);
                    commandsProcessedThisCall++;
                }
                commandBuffer = "";
            }
        } else if (c >= 32 && c <= 126) { // Printable characters only
            commandBuffer += c;

            // Prevent buffer overflow
            if (commandBuffer.length() >= COMMAND_BUFFER_SIZE - 1) {
                usbManager.sendDebugMessage("ERROR: Command buffer overflow");
                commandBuffer = "";
            }
        }
    }
}

void CommandProcessor::processCommandQueue() {
    HIDCommand cmd;
    
    // Process all available commands
    while (xQueueReceive(commandQueue, &cmd, 0) == pdTRUE) {
        if (!cmd.processed) {
            executeCommand(cmd);
            commandsProcessed++;
        }
    }
}

void CommandProcessor::sendHeartbeat() {
    uint32_t currentTime = millis();

    // REDUCED HEARTBEAT FREQUENCY - Only send when no recent commands
    // This prevents heartbeat from interfering with command responses
    if (currentTime - lastHeartbeat >= HEARTBEAT_INTERVAL) {
        lastHeartbeat = currentTime;

        // Only send heartbeat if no commands processed recently (last 2 seconds)
        static uint32_t lastCommandTime = 0;
        if (commandsProcessed > 0) {
            lastCommandTime = currentTime;
        }

        if (currentTime - lastCommandTime > 2000) {
            String status = "HEARTBEAT|" + String(systemInfo.getUptime()) + "|" +
                           String(commandsProcessed) + "|" + String(systemInfo.getFreeHeap()) +
                           "|" + String(usbManager.isReady() ? "USB_OK" : "USB_ERR");

            usbManager.sendDebugMessage(status);
        }
    }
}

uint32_t CommandProcessor::getCommandsProcessed() const {
    return commandsProcessed;
}

bool CommandProcessor::parseCommand(const String& command) {
    String trimmedCommand = command;
    trimmedCommand.trim();
    
    if (trimmedCommand.length() == 0) {
        return false;
    }
    
    // Try JSON format first
    if (trimmedCommand.startsWith("{") && trimmedCommand.endsWith("}")) {
        return parseJSONCommand(trimmedCommand);
    } else {
        return parseSimpleCommand(trimmedCommand);
    }
}

bool CommandProcessor::parseJSONCommand(const String& jsonStr) {
    DynamicJsonDocument doc(JSON_BUFFER_SIZE);
    DeserializationError error = deserializeJson(doc, jsonStr);
    
    if (error) {
        usbManager.sendDebugMessage("ERROR: JSON parse failed - " + String(error.c_str()));
        return false;
    }
    
    HIDCommand cmd = {CMD_STATUS, 0, 0, 0, millis(), false};
    
    if (doc.containsKey("type")) {
        String type = doc["type"];
        if (type == "move") {
            cmd.type = CMD_MOUSE_MOVE;
            cmd.x = doc["x"] | 0;
            cmd.y = doc["y"] | 0;
        } else if (type == "click") {
            cmd.type = doc["pressed"] ? CMD_CLICK_DOWN : CMD_CLICK_UP;
            cmd.button = doc["button"] | MOUSE_LEFT;
        } else if (type == "status") {
            cmd.type = CMD_STATUS;
        }
    }
    
    return queueCommand(cmd);
}

bool CommandProcessor::parseSimpleCommand(const String& command) {
    HIDCommand cmd = {CMD_STATUS, 0, 0, 0, millis(), false};
    
    if (command.startsWith("MOVE ")) {
        cmd.type = CMD_MOUSE_MOVE;
        int spaceIndex = command.indexOf(' ', 5);
        if (spaceIndex > 0) {
            cmd.x = command.substring(5, spaceIndex).toInt();
            cmd.y = command.substring(spaceIndex + 1).toInt();
        }
    } else if (command.startsWith("CLICK ")) {
        cmd.type = CMD_CLICK_DOWN;
        cmd.button = command.substring(6).toInt();
    } else if (command.startsWith("RELEASE ")) {
        cmd.type = CMD_CLICK_UP;
        cmd.button = command.substring(8).toInt();
    } else if (command == "STATUS") {
        cmd.type = CMD_STATUS;
    } else if (command == "ping" || command == "PING") {
        // IMMEDIATE PONG response for latency testing
        USBSerial.println("PONG|" + String(millis()));
        return true;
    } else if (command == "identify" || command == "IDENTIFY") {
        usbManager.sendDebugMessage("DEVICE|ESP32-S2|Octane|" + String(FIRMWARE_VERSION));
        return true;
    } else if (command.startsWith("MOUSE_MOVE ")) {
        // MOUSE_MOVE x,y - ULTRA-OPTIMIZED for zero-delay recoil control
        String params = command.substring(11);
        int commaPos = params.indexOf(',');
        if (commaPos > 0) {
            float deltaX = params.substring(0, commaPos).toFloat();
            float deltaY = params.substring(commaPos + 1).toFloat();

            // OPTIMIZATION: Accumulate small movements to reduce USB HID calls
            static float accumulatedX = 0.0f;
            static float accumulatedY = 0.0f;
            static uint32_t lastMoveTime = 0;

            accumulatedX += deltaX;
            accumulatedY += deltaY;

            uint32_t currentTime = millis();

            // Send movement if accumulated values are significant OR enough time has passed
            if (abs(accumulatedX) >= 1.0f || abs(accumulatedY) >= 1.0f || (currentTime - lastMoveTime) >= 5) {
                // IMMEDIATE EXECUTION - No queuing, no delays for perfect recoil
                usbManager.sendMouseMove(static_cast<int16_t>(accumulatedX), static_cast<int16_t>(accumulatedY));

                // Reset accumulation
                accumulatedX = 0.0f;
                accumulatedY = 0.0f;
                lastMoveTime = currentTime;
            }

            // OPTIMIZATION: Remove "OK" acknowledgments to reduce serial traffic
            // Only send acknowledgment for test commands or errors

            return true;
        } else {
            USBSerial.println("ERROR");
            return false;
        }
    } else if (command == "get_version" || command == "GET_VERSION") {
        usbManager.sendDebugMessage("VERSION|" + String(FIRMWARE_VERSION));
        return true;
    } else if (command.startsWith("MOUSE_BATCH ")) {
        // MOUSE_BATCH x1,y1;x2,y2;x3,y3 - Process multiple movements in one command
        String params = command.substring(12);
        int totalX = 0, totalY = 0;
        int moveCount = 0;

        // Parse semicolon-separated movements
        int startPos = 0;
        int semiPos = params.indexOf(';');

        while (startPos < params.length() && moveCount < 10) { // Limit to 10 movements per batch
            String moveStr;
            if (semiPos >= 0) {
                moveStr = params.substring(startPos, semiPos);
                startPos = semiPos + 1;
                semiPos = params.indexOf(';', startPos);
            } else {
                moveStr = params.substring(startPos);
                startPos = params.length();
            }

            int commaPos = moveStr.indexOf(',');
            if (commaPos > 0) {
                int deltaX = moveStr.substring(0, commaPos).toInt();
                int deltaY = moveStr.substring(commaPos + 1).toInt();
                totalX += deltaX;
                totalY += deltaY;
                moveCount++;
            }
        }

        if (moveCount > 0) {
            // Send accumulated movement
            usbManager.sendMouseMove(static_cast<int16_t>(totalX), static_cast<int16_t>(totalY));
            return true;
        } else {
            USBSerial.println("ERROR");
            return false;
        }
    } else if (command.startsWith("BLINK ")) {
        // BLINK count
        int count = command.substring(6).toInt();
        if (count > 0 && count <= 20) {
            ledController.setState(LED_ACTIVE); // Use active state for blinking
            usbManager.sendDebugMessage("Blinking LED " + String(count) + " times");
            return true;
        } else {
            usbManager.sendDebugMessage("ERROR: Invalid blink count (1-20)");
            return false;
        }
    } else if (command.startsWith("RECOIL_SMOOTH ")) {
        // Advanced recoil smoothing: RECOIL_SMOOTH totalX,totalY,steps,delayPerStep
        String params = command.substring(14);
        int pos1 = params.indexOf(',');
        int pos2 = params.indexOf(',', pos1 + 1);
        int pos3 = params.indexOf(',', pos2 + 1);

        if (pos1 > 0 && pos2 > 0 && pos3 > 0) {
            float totalX = params.substring(0, pos1).toFloat();
            float totalY = params.substring(pos1 + 1, pos2).toFloat();
            int steps = params.substring(pos2 + 1, pos3).toInt();
            int delayPerStep = params.substring(pos3 + 1).toInt();

            executeSmoothRecoil(totalX, totalY, steps, delayPerStep);
            return true;
        } else {
            usbManager.sendDebugMessage("ERROR: Invalid RECOIL_SMOOTH format");
            return false;
        }
    } else {
        usbManager.sendDebugMessage("ERROR: Unknown command - " + command);
        return false;
    }
    
    return queueCommand(cmd);
}

bool CommandProcessor::queueCommand(const HIDCommand& cmd) {
    if (commandQueue == nullptr) {
        usbManager.sendDebugMessage("ERROR: Command queue not initialized");
        return false;
    }
    
    if (xQueueSend(commandQueue, &cmd, 0) != pdTRUE) {
        usbManager.sendDebugMessage("ERROR: Command queue full");
        return false;
    }
    
    return true;
}

void CommandProcessor::executeCommand(const HIDCommand& cmd) {
    switch (cmd.type) {
        case CMD_MOUSE_MOVE:
            // ULTRA-FAST EXECUTION - Zero delay mouse movement for recoil
            usbManager.sendMouseMove(cmd.x, cmd.y);
            // Send immediate acknowledgment for recoil commands
            USBSerial.println("OK");
            break;

        case CMD_CLICK_DOWN:
            usbManager.sendMouseClick(cmd.button, true);
            USBSerial.println("OK");
            break;

        case CMD_CLICK_UP:
            usbManager.sendMouseClick(cmd.button, false);
            USBSerial.println("OK");
            break;

        case CMD_STATUS:
            sendStatusResponse();
            break;

        case CMD_PING:
            // Immediate PONG response for latency testing
            USBSerial.println("PONG|" + String(millis()));
            break;
    }
}

void CommandProcessor::sendStatusResponse() {
    // ULTRA-FAST STATUS RESPONSE - Minimal data for maximum speed
    String response = "STATUS|OK|" + String(systemInfo.getUptime()) + "|" +
                     String(commandsProcessed) + "|" + String(systemInfo.getFreeHeap());
    // Send directly via USBSerial for fastest response
    USBSerial.println(response);
}

void CommandProcessor::sendSystemInfo() {
    usbManager.sendDebugMessage("SYSTEM|" + systemInfo.getChipModel() + "|" +
                               String(systemInfo.getCpuFreqMHz()) + "MHz|" +
                               String(systemInfo.getFreeHeap()) + "B");
}

void CommandProcessor::executeSmoothRecoil(float totalX, float totalY, int steps, int delayPerStep) {
    if (steps <= 0) steps = 1;

    float stepX = totalX / steps;
    float stepY = totalY / steps;

    // Static accumulators to maintain precision across calls (based on your algorithm)
    static float accumX = 0.0f;
    static float accumY = 0.0f;

    usbManager.sendDebugMessage("SMOOTH_RECOIL: " + String(totalX) + "," + String(totalY) +
                               " steps=" + String(steps) + " delay=" + String(delayPerStep));

    for (int i = 0; i < steps; ++i) {
        accumX += stepX;
        accumY += stepY;

        // Only move when we have at least 1 pixel of movement (your precision approach)
        if (abs(accumX) >= 1.0f || abs(accumY) >= 1.0f) {
            int moveX = static_cast<int>(accumX);
            int moveY = static_cast<int>(accumY);

            // Send mouse movement via HID
            usbManager.sendMouseMove(static_cast<int16_t>(moveX), static_cast<int16_t>(moveY));

            // Subtract the movement we just made from accumulators
            accumX -= moveX;
            accumY -= moveY;
        }

        // ZERO DELAY OPTIMIZATION - Remove delays for maximum responsiveness
        // Template-style immediate execution for perfect recoil control
        // No artificial delays - let the C++ application control timing
    }
}
