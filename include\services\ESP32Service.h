#pragma once
#include <string>
#include <vector>
#include <windows.h>

namespace octane {
namespace services {

/**
 * @brief Service for communicating with ESP32 devices
 */
class ESP32Service {
public:
    ESP32Service();
    ~ESP32Service();

    /**
     * @brief Initialize the ESP32 service
     */
    bool initialize();

    /**
     * @brief Connect to an ESP32 device
     */
    bool connectToDevice(const std::string& port);

    /**
     * @brief Disconnect from the current ESP32 device
     */
    void disconnect();

    /**
     * @brief Check if connected to an ESP32 device
     */
    bool isConnected() const;

    /**
     * @brief Get available COM ports
     */
    std::vector<std::string> getAvailablePorts();

    /**
     * @brief Send recoil compensation data to ESP32
     */
    bool sendRecoilData(float x, float y);

    /**
     * @brief Send a command string to ESP32
     */
    bool sendCommand(const std::string& command);

private:
    bool m_connected = false;
    std::string m_currentPort;
    HANDLE m_serialHandle = INVALID_HANDLE_VALUE;

    // Helper methods
    bool openSerialPort(const std::string& port);
    void closeSerialPort();
    bool writeToSerial(const std::string& data);
};

} // namespace services
} // namespace octane
