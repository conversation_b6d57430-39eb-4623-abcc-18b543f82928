@echo off
echo Testing Visual Studio build...

REM Copy WebView2Loader.dll to output directory
copy "packages\Microsoft.Web.WebView2.1.0.2210.55\build\native\x64\WebView2Loader.dll" "." 2>nul

REM Build with MSBuild
"C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" OctaneRecoilController.vcxproj /p:Configuration=Release /p:Platform=x64

if %ERRORLEVEL% EQU 0 (
    echo Build successful!
    echo Running application...
    Release\OctaneRecoilController.exe
) else (
    echo Build failed!
    pause
)
