/*
 * Template with ESP32 Hardware Control
 * 
 * This is the original template modified to use ESP32 for mouse control
 * Settings: Sensitivity=0.6, ADS=0.6, ESP32=Enabled
 * 
 * Compare this directly with the C++ application to see performance differences
 */

#include <iostream>
#include <windows.h>
#include "core/core.hpp"
#include "dependencies/global/global.hpp"

int main()
{
    std::cout << "========================================" << std::endl;
    std::cout << "TEMPLATE WITH ESP32 HARDWARE CONTROL" << std::endl;
    std::cout << "========================================" << std::endl;
    std::cout << std::endl;
    
    std::cout << "Configuration:" << std::endl;
    std::cout << "  Sensitivity: " << m_global::m_settings::sensitivity << std::endl;
    std::cout << "  ADS Sensitivity: " << m_global::m_settings::ads_sensitivity << std::endl;
    std::cout << "  FOV: " << m_global::m_settings::field_of_view << std::endl;
    std::cout << "  Recoil Control: " << (m_global::m_settings::recoil_control ? "Enabled" : "Disabled") << std::endl;
    std::cout << std::endl;
    
    std::cout << "Initializing template..." << std::endl;
    
    // Cache weapon data
    if (!m_core::static_cache()) {
        std::cout << "Failed to cache weapon data!" << std::endl;
        return 1;
    }
    
    std::cout << "Weapon data cached successfully" << std::endl;
    std::cout << std::endl;
    
    std::cout << "🎯 TEMPLATE READY!" << std::endl;
    std::cout << "Instructions:" << std::endl;
    std::cout << "  1. Make sure ESP32 is connected to COM13" << std::endl;
    std::cout << "  2. Hold LEFT MOUSE BUTTON to activate recoil control" << std::endl;
    std::cout << "  3. Use CTRL to crouch (reduces recoil by 50%)" << std::endl;
    std::cout << "  4. Use WASD for movement detection" << std::endl;
    std::cout << "  5. Press CTRL+C to exit" << std::endl;
    std::cout << std::endl;
    
    std::cout << "Starting recoil control loop..." << std::endl;
    std::cout << "========================================" << std::endl;
    
    // Start the main recoil control loop
    m_core::initialize();
    
    return 0;
}
