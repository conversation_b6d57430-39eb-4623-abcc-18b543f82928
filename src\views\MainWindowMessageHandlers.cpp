#include "views/MainWindow.h"
#include "views/MainWindowHtml.h"
#include "models/WeaponData.h"
#include "security/LicenseManager.h"
#include "services/ESP32Service_NoQt.h"
#include "services/RecoilControlService.h"
#include "services/SoundService.h"
#include "utils/ConfigManager.h"
#include "config/AppConfig.h"
#include <iostream>
#include <wrl.h>
#include <algorithm>
#include "WebView2.h"

#ifdef _WIN32
#include <windows.h>
#endif

using namespace Microsoft::WRL;

namespace octane {
namespace views {

void MainWindow::handleWebViewMessage(const std::string& message)
{
    std::cout << "Received message from WebView: " << message << std::endl;

    // Simple message parsing (in a real implementation, use JSON parser)
    if (message.find("\"type\":\"validateLicense\"") != std::string::npos) {
        // Extract license key from message
        size_t keyStart = message.find("\"key\":\"");
        if (keyStart != std::string::npos) {
            keyStart += 7; // Skip past "key":"
            size_t keyEnd = message.find("\"", keyStart);
            if (keyEnd != std::string::npos) {
                std::string key = message.substr(keyStart, keyEnd - keyStart);
                std::cout << "Extracted license key: " << key << std::endl;
                handleLicenseValidation(key);
            } else {
                std::cout << "Could not find end of license key" << std::endl;
            }
        } else {
            std::cout << "Could not find license key in message" << std::endl;
        }
    } else if (message.find("\"type\":\"exitApplication\"") != std::string::npos) {
        std::cout << "Exit application requested" << std::endl;
        m_shouldClose = true;
    } else if (message.find("\"type\":\"weaponChanged\"") != std::string::npos) {
        // Extract weapon name from message
        size_t weaponStart = message.find("\"weapon\":\"");
        if (weaponStart != std::string::npos) {
            weaponStart += 10; // Length of "weapon":""
            size_t weaponEnd = message.find("\"", weaponStart);
            if (weaponEnd != std::string::npos) {
                std::string weaponName = message.substr(weaponStart, weaponEnd - weaponStart);
                std::cout << "Weapon changed to: " << weaponName << std::endl;

                // Set weapon in recoil service
                if (m_recoilService) {
                    m_recoilService->setWeaponByName(weaponName);
                }
            }
        }
    } else if (message.find("\"type\":\"recoilToggled\"") != std::string::npos) {
        // Extract enabled state from message
        bool enabled = message.find("\"enabled\":true") != std::string::npos;
        std::cout << "Recoil " << (enabled ? "enabled" : "disabled") << std::endl;

        // Set recoil enabled state
        if (m_recoilService) {
            m_recoilService->setEnabled(enabled);
        }
    } else if (message.find("\"type\":\"humanizationToggled\"") != std::string::npos) {
        // Extract enabled state from message
        bool enabled = message.find("\"enabled\":true") != std::string::npos;
        std::cout << "Humanization " << (enabled ? "enabled" : "disabled") << std::endl;

        // Set humanization enabled state
        if (m_recoilService) {
            m_recoilService->setHumanizationEnabled(enabled);
        }
    } else if (message.find("\"type\":\"captureHumanizationKeybind\"") != std::string::npos) {
        std::cout << "Capturing humanization keybind..." << std::endl;
        // TODO: Implement humanization keybind capture
    } else if (message.find("\"type\":\"humanizationRandomizationChanged\"") != std::string::npos) {
        // Extract randomization value from message
        size_t valueStart = message.find("\"value\":");
        if (valueStart != std::string::npos) {
            valueStart += 8; // Length of "value":
            size_t valueEnd = message.find_first_of(",}", valueStart);
            if (valueEnd != std::string::npos) {
                std::string valueStr = message.substr(valueStart, valueEnd - valueStart);
                int randomization = std::stoi(valueStr);
                std::cout << "Humanization randomization changed to: " << randomization << std::endl;

                // Update recoil service humanization randomization
                if (m_recoilService) {
                    m_recoilService->setHumanizationRandomization(randomization);
                }
            }
        }
    } else if (message.find("\"type\":\"humanizationLevelChanged\"") != std::string::npos) {
        // Extract humanization level value from message
        size_t valueStart = message.find("\"value\":");
        if (valueStart != std::string::npos) {
            valueStart += 8; // Length of "value":
            size_t valueEnd = message.find_first_of(",}", valueStart);
            if (valueEnd != std::string::npos) {
                std::string valueStr = message.substr(valueStart, valueEnd - valueStart);
                int level = std::stoi(valueStr);
                std::cout << "Humanization level changed to: " << level << std::endl;

                // Update recoil service humanization level
                if (m_recoilService) {
                    m_recoilService->setHumanizationLevel(level);
                }
            }
        }
    } else if (message.find("\"type\":\"initialized\"") != std::string::npos) {
        std::cout << "WebView initialized, showing license modal" << std::endl;
        // Always show license modal, but validation behavior depends on mode
        showLicenseModal();
    } else if (message.find("\"type\":\"changeLicense\"") != std::string::npos) {
        std::cout << "Change license requested" << std::endl;
        showLicenseModal();
    } else if (message.find("\"type\":\"refreshPorts\"") != std::string::npos) {
        std::cout << "Refresh ports requested" << std::endl;
        handleRefreshPorts();
    } else if (message.find("\"type\":\"connectESP32\"") != std::string::npos) {
        std::cout << "ESP32 connection requested" << std::endl;
        handleESP32Connection(message);
    } else if (message.find("\"type\":\"disconnectESP32\"") != std::string::npos) {
        std::cout << "ESP32 disconnection requested" << std::endl;
        handleESP32Disconnection();
    } else if (message.find("\"type\":\"sensitivityChanged\"") != std::string::npos) {
        // Extract sensitivity value from message
        size_t valueStart = message.find("\"value\":");
        if (valueStart != std::string::npos) {
            valueStart += 8; // Length of "value":
            size_t valueEnd = message.find_first_of(",}", valueStart);
            if (valueEnd != std::string::npos) {
                std::string valueStr = message.substr(valueStart, valueEnd - valueStart);
                float sensitivity = std::stof(valueStr);
                std::cout << "Sensitivity changed to: " << sensitivity << std::endl;

                // Update recoil service sensitivity
                if (m_recoilService) {
                    m_recoilService->setSensitivity(sensitivity);
                }
            }
        }
    } else if (message.find("\"type\":\"adsSensitivityChanged\"") != std::string::npos) {
        // Extract ADS sensitivity value from message
        size_t valueStart = message.find("\"value\":");
        if (valueStart != std::string::npos) {
            valueStart += 8; // Length of "value":
            size_t valueEnd = message.find_first_of(",}", valueStart);
            if (valueEnd != std::string::npos) {
                std::string valueStr = message.substr(valueStart, valueEnd - valueStart);
                float adsSensitivity = std::stof(valueStr);
                std::cout << "ADS Sensitivity changed to: " << adsSensitivity << std::endl;

                // Update recoil service ADS sensitivity
                if (m_recoilService) {
                    m_recoilService->setAdsSensitivity(adsSensitivity);
                }
            }
        }
    } else if (message.find("\"type\":\"fovChanged\"") != std::string::npos) {
        // Extract FOV value from message
        size_t valueStart = message.find("\"value\":");
        if (valueStart != std::string::npos) {
            valueStart += 8; // Length of "value":
            size_t valueEnd = message.find_first_of(",}", valueStart);
            if (valueEnd != std::string::npos) {
                std::string valueStr = message.substr(valueStart, valueEnd - valueStart);
                float fov = std::stof(valueStr);
                std::cout << "FOV changed to: " << fov << std::endl;

                // Update recoil service FOV
                if (m_recoilService) {
                    m_recoilService->setFOV(fov);
                }
            }
        }
    } else {
        // Handle additional message types in continuation
        handleAdditionalMessages(message);
    }
}

void MainWindow::handleAdditionalMessages(const std::string& message)
{
    if (message.find("\"type\":\"saveConfig\"") != std::string::npos) {
        handleSaveConfig(message);
    } else if (message.find("\"type\":\"loadConfig\"") != std::string::npos) {
        handleLoadConfig(message);
    } else if (message.find("\"type\":\"deleteConfig\"") != std::string::npos) {
        handleDeleteConfig(message);
    } else if (message.find("\"type\":\"refreshConfigs\"") != std::string::npos) {
        handleRefreshConfigs();
    } else if (message.find("\"type\":\"autoLoadConfigChanged\"") != std::string::npos) {
        handleAutoLoadConfigChanged(message);
    } else if (message.find("\"type\":\"cursorCheckChanged\"") != std::string::npos) {
        bool enabled = message.find("\"enabled\":true") != std::string::npos;
        std::cout << "Cursor check " << (enabled ? "enabled" : "disabled") << std::endl;

        // Update recoil service cursor check setting
        if (m_recoilService) {
            m_recoilService->setCursorCheckEnabled(enabled);
        }
    } else if (message.find("\"type\":\"smoothnessChanged\"") != std::string::npos) {
        size_t valueStart = message.find("\"value\":");
        if (valueStart != std::string::npos) {
            valueStart += 8;
            size_t valueEnd = message.find_first_of(",}", valueStart);
            if (valueEnd != std::string::npos) {
                std::string valueStr = message.substr(valueStart, valueEnd - valueStart);
                int smoothness = std::stoi(valueStr);
                std::cout << "Recoil smoothness changed to: " << smoothness << "%" << std::endl;

                // Update recoil service smoothness
                if (m_recoilService) {
                    m_recoilService->setSmoothness(smoothness);
                }
            }
        }
    } else {
        handleFeatureMessages(message);
    }
}

void MainWindow::handleFeatureMessages(const std::string& message)
{
    if (message.find("\"type\":\"keybindSoundsChanged\"") != std::string::npos) {
        bool enabled = message.find("\"enabled\":true") != std::string::npos;
        std::cout << "Keybind sounds " << (enabled ? "enabled" : "disabled") << std::endl;

        // Update sound service
        if (m_soundService) {
            m_soundService->setEnabled(enabled);
        }
    } else if (message.find("\"type\":\"autoCodeLockChanged\"") != std::string::npos) {
        bool enabled = message.find("\"enabled\":true") != std::string::npos;
        std::cout << "Auto code lock " << (enabled ? "enabled" : "disabled") << std::endl;

        // Update recoil service auto code lock setting
        if (m_recoilService) {
            m_recoilService->setAutoCodeLockEnabled(enabled);
        }
    } else if (message.find("\"type\":\"codeLockDigitsChanged\"") != std::string::npos) {
        size_t digitsStart = message.find("\"digits\":\"");
        if (digitsStart != std::string::npos) {
            digitsStart += 10;
            size_t digitsEnd = message.find("\"", digitsStart);
            if (digitsEnd != std::string::npos) {
                std::string digits = message.substr(digitsStart, digitsEnd - digitsStart);
                std::cout << "Code lock digits changed to: " << digits << std::endl;

                // Update recoil service code lock digits
                if (m_recoilService) {
                    m_recoilService->setCodeLockDigits(digits);
                }
            }
        }
    } else if (message.find("\"type\":\"rapidFireChanged\"") != std::string::npos) {
        bool enabled = message.find("\"enabled\":true") != std::string::npos;
        std::cout << "Rapid fire " << (enabled ? "enabled" : "disabled") << std::endl;

        // Update recoil service rapid fire setting
        if (m_recoilService) {
            m_recoilService->setRapidFireEnabled(enabled);
        }
    } else if (message.find("\"type\":\"autoClickerChanged\"") != std::string::npos) {
        bool enabled = message.find("\"enabled\":true") != std::string::npos;
        std::cout << "Auto clicker " << (enabled ? "enabled" : "disabled") << std::endl;

        // Update recoil service auto clicker setting
        if (m_recoilService) {
            m_recoilService->setAutoClickerEnabled(enabled);
        }
    } else {
        handleMoreFeatureMessages(message);
    }
}

void MainWindow::handleMoreFeatureMessages(const std::string& message)
{
    if (message.find("\"type\":\"antiAfkChanged\"") != std::string::npos) {
        bool enabled = message.find("\"enabled\":true") != std::string::npos;
        std::cout << "Anti AFK " << (enabled ? "enabled" : "disabled") << std::endl;

        // Update recoil service anti AFK setting
        if (m_recoilService) {
            m_recoilService->setAntiAfkEnabled(enabled);
        }
    } else if (message.find("\"type\":\"afkIntervalChanged\"") != std::string::npos) {
        size_t valueStart = message.find("\"value\":");
        if (valueStart != std::string::npos) {
            valueStart += 8;
            size_t valueEnd = message.find_first_of(",}", valueStart);
            if (valueEnd != std::string::npos) {
                std::string valueStr = message.substr(valueStart, valueEnd - valueStart);
                int interval = std::stoi(valueStr);
                std::cout << "AFK interval changed to: " << interval << "s" << std::endl;

                // Update recoil service AFK interval
                if (m_recoilService) {
                    m_recoilService->setAfkInterval(interval);
                }
            }
        }
    } else if (message.find("\"type\":\"keybindChanged\"") != std::string::npos) {
        // Handle keybind changes
        size_t actionStart = message.find("\"action\":\"");
        size_t keyStart = message.find("\"key\":\"");

        if (actionStart != std::string::npos && keyStart != std::string::npos) {
            actionStart += 10;
            size_t actionEnd = message.find("\"", actionStart);

            keyStart += 7;
            size_t keyEnd = message.find("\"", keyStart);

            if (actionEnd != std::string::npos && keyEnd != std::string::npos) {
                std::string action = message.substr(actionStart, actionEnd - actionStart);
                std::string key = message.substr(keyStart, keyEnd - keyStart);

                std::cout << "Keybind changed: " << action << " = " << key << std::endl;

                // Store keybind in the recoil service
                if (m_recoilService) {
                    if (key.empty()) {
                        m_recoilService->clearKeybind(action);
                    } else {
                        m_recoilService->setKeybind(action, key);
                    }
                }
            }
        }
    } else {
        handleRandomizationMessages(message);
    }
}

void MainWindow::handleRandomizationMessages(const std::string& message)
{
    if (message.find("\"type\":\"compensateHipfireChanged\"") != std::string::npos) {
        bool enabled = message.find("\"enabled\":true") != std::string::npos;
        std::cout << "Compensate hipfire " << (enabled ? "enabled" : "disabled") << std::endl;

        if (m_recoilService) {
            m_recoilService->setCompensateHipfire(enabled);
        }
    } else if (message.find("\"type\":\"hipfireModeChanged\"") != std::string::npos) {
        size_t modeStart = message.find("\"mode\":\"");
        if (modeStart != std::string::npos) {
            modeStart += 8; // Length of "mode":"
            size_t modeEnd = message.find("\"", modeStart);
            if (modeEnd != std::string::npos) {
                std::string mode = message.substr(modeStart, modeEnd - modeStart);
                std::cout << "Hipfire mode changed to: " << mode << std::endl;

                if (m_recoilService) {
                    m_recoilService->setHipfireMode(mode);
                }
            }
        }
    } else if (message.find("\"type\":\"horizontalRandomizationChanged\"") != std::string::npos) {
        size_t valueStart = message.find("\"value\":");
        if (valueStart != std::string::npos) {
            valueStart += 8;
            size_t valueEnd = message.find_first_of(",}", valueStart);
            if (valueEnd != std::string::npos) {
                std::string valueStr = message.substr(valueStart, valueEnd - valueStart);
                int value = std::stoi(valueStr);
                std::cout << "Horizontal randomization changed to: " << value << std::endl;

                if (m_recoilService) {
                    m_recoilService->setHorizontalRandomization(value);
                }
            }
        }
    } else if (message.find("\"type\":\"verticalRandomizationChanged\"") != std::string::npos) {
        size_t valueStart = message.find("\"value\":");
        if (valueStart != std::string::npos) {
            valueStart += 8;
            size_t valueEnd = message.find_first_of(",}", valueStart);
            if (valueEnd != std::string::npos) {
                std::string valueStr = message.substr(valueStart, valueEnd - valueStart);
                int value = std::stoi(valueStr);
                std::cout << "Vertical randomization changed to: " << value << std::endl;

                if (m_recoilService) {
                    m_recoilService->setVerticalRandomization(value);
                }
            }
        }
    } else if (message.find("\"type\":\"weaponKeybindChanged\"") != std::string::npos) {
        // Handle weapon keybind changes
        size_t weaponStart = message.find("\"weapon\":\"");
        size_t keyStart = message.find("\"key\":\"");

        if (weaponStart != std::string::npos && keyStart != std::string::npos) {
            weaponStart += 10; // Length of "weapon":"
            size_t weaponEnd = message.find("\"", weaponStart);

            keyStart += 7; // Length of "key":"
            size_t keyEnd = message.find("\"", keyStart);

            if (weaponEnd != std::string::npos && keyEnd != std::string::npos) {
                std::string weapon = message.substr(weaponStart, weaponEnd - weaponStart);
                std::string key = message.substr(keyStart, keyEnd - keyStart);

                std::cout << "Weapon keybind changed: " << weapon << " -> " << key << std::endl;

                // TODO: Store weapon keybind mapping
                // This could be stored in a configuration file or service
            }
        }
    } else {
        std::cout << "Unknown message type: " << message << std::endl;
    }
}

} // namespace views
} // namespace octane
