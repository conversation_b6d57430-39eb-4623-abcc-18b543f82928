#include <iostream>
#include <vector>
#include <chrono>
#include <thread>
#include <windows.h>
#include <string>
#include <fstream>

// ===== TEMPLATE SETTINGS (User requested values) =====
namespace settings {
    float sensitivity = 0.6f;      // User requested 0.6
    float ads_sensitivity = 0.6f;  // User requested 0.6  
    float field_of_view = 90.0f;   // User requested 90
}

// ===== ESP32 COMMUNICATION =====
class ESP32Service {
private:
    HANDLE hSerial;
    bool connected;
    
public:
    ESP32Service() : hSerial(INVALID_HANDLE_VALUE), connected(false) {}
    
    bool connect(const std::string& port) {
        std::string fullPort = "\\\\.\\" + port;
        hSerial = CreateFileA(fullPort.c_str(), GENERIC_READ | GENERIC_WRITE, 0, NULL, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, NULL);
        
        if (hSerial == INVALID_HANDLE_VALUE) {
            std::cout << "❌ Failed to connect to " << port << std::endl;
            return false;
        }
        
        // Configure serial port
        DCB dcbSerialParams = {0};
        dcbSerialParams.DCBlength = sizeof(dcbSerialParams);
        if (!GetCommState(hSerial, &dcbSerialParams)) {
            CloseHandle(hSerial);
            return false;
        }
        
        dcbSerialParams.BaudRate = CBR_115200;
        dcbSerialParams.ByteSize = 8;
        dcbSerialParams.StopBits = ONESTOPBIT;
        dcbSerialParams.Parity = NOPARITY;
        
        if (!SetCommState(hSerial, &dcbSerialParams)) {
            CloseHandle(hSerial);
            return false;
        }
        
        connected = true;
        std::cout << "✅ ESP32 connected to " << port << std::endl;
        return true;
    }
    
    void sendMouseMove(int deltaX, int deltaY) {
        if (!connected) return;
        
        std::string command = "MOUSE_MOVE " + std::to_string(deltaX) + "," + std::to_string(deltaY) + "\n";
        DWORD bytesWritten;
        WriteFile(hSerial, command.c_str(), command.length(), &bytesWritten, NULL);
        
        // Debug output
        std::cout << "ESP32 -> " << deltaX << ", " << deltaY << std::endl;
    }
    
    bool isConnected() const { return connected; }
    
    ~ESP32Service() {
        if (hSerial != INVALID_HANDLE_VALUE) {
            CloseHandle(hSerial);
        }
    }
};

// ===== VECTOR2 STRUCTURE =====
struct Vector2 {
    float x, y;
    Vector2(float x = 0, float y = 0) : x(x), y(y) {}
    Vector2 operator*(float scalar) const { return Vector2(x * scalar, y * scalar); }
};

// ===== AK47 RECOIL PATTERN (Exact from template) =====
std::vector<Vector2> ak47_pattern = {
    Vector2(0.000000f, -2.257792f), Vector2(0.323242f, -2.300758f), Vector2(0.649593f, -2.299759f),
    Vector2(0.848786f, -2.259030f), Vector2(1.075410f, -2.323950f), Vector2(1.268490f, -2.215960f),
    Vector2(1.330960f, -2.236560f), Vector2(1.336830f, -2.218200f), Vector2(1.505520f, -2.143450f),
    Vector2(1.504420f, -2.233090f), Vector2(1.442120f, -2.270190f), Vector2(1.478540f, -2.204320f),
    Vector2(1.392870f, -2.165820f), Vector2(1.480820f, -2.177890f), Vector2(1.597070f, -2.270920f),
    Vector2(1.450000f, -2.145890f), Vector2(1.369180f, -2.270450f), Vector2(1.582360f, -2.298330f),
    Vector2(1.516870f, -2.235070f), Vector2(1.498250f, -2.238400f), Vector2(1.465770f, -2.331640f),
    Vector2(1.564810f, -2.242620f), Vector2(1.517520f, -2.303050f), Vector2(1.422430f, -2.211950f),
    Vector2(1.553190f, -2.248040f), Vector2(1.510460f, -2.285330f), Vector2(1.553880f, -2.240050f),
    Vector2(1.520380f, -2.221840f), Vector2(1.553880f, -2.240050f), Vector2(1.553190f, -2.248040f)
};

// ===== TEMPLATE TIMER CLASS =====
class Timer {
private:
    std::chrono::high_resolution_clock::time_point start_time;
    bool running;
    
public:
    Timer() : running(false) {}
    
    void start() {
        start_time = std::chrono::high_resolution_clock::now();
        running = true;
    }
    
    void end() {
        running = false;
    }
    
    int elapsed_time_ms() {
        auto current_time = running ? std::chrono::high_resolution_clock::now() : start_time;
        return std::chrono::duration_cast<std::chrono::milliseconds>(current_time - start_time).count();
    }
};

// ===== TEMPLATE CONVERSION FUNCTION =====
Vector2 to_pixel(Vector2 input) {
    // EXACT TEMPLATE FORMULA from core.cpp
    float scale = -0.03f * (settings::sensitivity * settings::ads_sensitivity * 3.0f) * (settings::field_of_view / 100.0f);
    
    float pixelX = std::round(input.x / scale);
    float pixelY = std::round(input.y / scale);
    
    return Vector2(pixelX, pixelY);
}

// ===== MOVEMENT DETECTION =====
bool isWalking() {
    return (GetAsyncKeyState('W') & 0x8000) || (GetAsyncKeyState('A') & 0x8000) || 
           (GetAsyncKeyState('S') & 0x8000) || (GetAsyncKeyState('D') & 0x8000);
}

bool isCrouching() {
    return (GetAsyncKeyState(VK_CONTROL) & 0x8000) && !isWalking();
}

bool isCrouchWalking() {
    return (GetAsyncKeyState(VK_CONTROL) & 0x8000) && isWalking();
}

float getMovementMultiplier() {
    if (isCrouchWalking()) return 0.33f;  // Template: crouch + walk
    if (isCrouching()) return 0.5f;       // Template: crouch only  
    if (isWalking()) return 0.66f;        // Template: walk only
    return 1.0f;                          // Template: standing still
}

// ===== GLOBAL VARIABLES =====
ESP32Service esp32;
std::vector<Vector2> cached_pattern;

// ===== MAIN RECOIL FUNCTION (Exact template logic) =====
void recoil_control_loop() {
    std::cout << "\n🎯 TEMPLATE ESP32 RECOIL CONTROL ACTIVE" << std::endl;
    std::cout << "Settings: Sens=" << settings::sensitivity << ", ADS=" << settings::ads_sensitivity << ", FOV=" << settings::field_of_view << std::endl;
    std::cout << "Hold LEFT MOUSE BUTTON to activate recoil control" << std::endl;
    std::cout << "Use CTRL for crouch, WASD for movement detection" << std::endl;
    std::cout << "========================================" << std::endl;
    
    Timer timer;
    const int WEAPON_DELAY = 133; // AK47 delay from template
    
    while (true) {
        // Template logic: Check if LEFT mouse button is pressed
        if (GetAsyncKeyState(VK_LBUTTON) & 0x8000) {
            
            std::cout << "\n🔥 RECOIL SEQUENCE STARTED" << std::endl;
            
            // Execute full pattern like template
            for (size_t bullet_index = 0; bullet_index < cached_pattern.size() && (GetAsyncKeyState(VK_LBUTTON) & 0x8000); ++bullet_index) {
                timer.start();
                
                auto recoil_data = cached_pattern[bullet_index];
                
                // Apply crouch modifier (template: data *= 0.5f)
                if (GetAsyncKeyState(VK_CONTROL) & 0x8000) {
                    recoil_data = recoil_data * 0.5f;
                }
                
                // Apply movement multiplier (template system)
                float movement_mult = getMovementMultiplier();
                recoil_data = recoil_data * movement_mult;
                
                // Send to ESP32 (replaces template mouse movement)
                esp32.sendMouseMove(static_cast<int>(recoil_data.x), static_cast<int>(recoil_data.y));
                
                std::cout << "Bullet " << (bullet_index + 1) << ": " << recoil_data.x << ", " << recoil_data.y 
                          << " (mult: " << movement_mult << ")" << std::endl;
                
                timer.end();
                
                // Template timing: weapon_delay - elapsed_time
                int remaining_delay = WEAPON_DELAY - timer.elapsed_time_ms();
                if (remaining_delay > 0) {
                    std::this_thread::sleep_for(std::chrono::milliseconds(remaining_delay));
                }
            }
            
            std::cout << "🎯 RECOIL SEQUENCE COMPLETED" << std::endl;
        }
        
        // Small sleep to prevent CPU overload
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
}

// ===== MAIN FUNCTION =====
int main() {
    std::cout << "🚀 TEMPLATE ESP32 RECOIL CONTROLLER v1.0" << std::endl;
    std::cout << "========================================" << std::endl;
    
    // Connect to ESP32
    if (!esp32.connect("COM13")) {
        std::cout << "❌ Failed to connect to ESP32 on COM13" << std::endl;
        std::cout << "Press any key to exit..." << std::endl;
        std::cin.get();
        return 1;
    }
    
    // Cache weapon pattern (template approach)
    std::cout << "📊 Caching AK47 recoil pattern..." << std::endl;
    cached_pattern.clear();
    for (const auto& point : ak47_pattern) {
        cached_pattern.push_back(to_pixel(point));
    }
    std::cout << "✅ Cached " << cached_pattern.size() << " recoil points" << std::endl;
    
    // Start recoil control
    recoil_control_loop();
    
    return 0;
}
