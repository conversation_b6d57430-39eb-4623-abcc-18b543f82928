{"test_start_time": "2025-07-30T16:30:10.487328", "esp32_connected": true, "total_configurations": 8, "configuration_results": [{"config_name": "Standard_1.0_Sens", "total_tests": 8, "passed": 3, "failed": 5, "warnings": 0, "errors": ["Mouse movement inaccurate for bullet 1: Expected(0.0, 28.0) vs Actual(0, 32)", "Mouse movement inaccurate for bullet 2: Expected(-4.0, 28.0) vs Actual(-3, 31)", "Mouse movement inaccurate for bullet 3: Expected(-8.0, 28.0) vs Actual(-9, 32)", "Mouse movement inaccurate for bullet 4: Expected(-10.0, 28.0) vs Actual(-12, 31)", "Mouse movement inaccurate for bullet 5: Expected(-13.0, 29.0) vs Actual(-14, 33)"], "esp32_commands": [{"timestamp": 1753889410.4895635, "command": "MOUSE_MOVE 0.0,28.0", "x": 0.0, "y": 28.0, "bullet_index": 0}, {"timestamp": 1753889410.724257, "command": "MOUSE_MOVE -4.0,28.0", "x": -4.0, "y": 28.0, "bullet_index": 1}, {"timestamp": 1753889410.9582994, "command": "MOUSE_MOVE -8.0,28.0", "x": -8.0, "y": 28.0, "bullet_index": 2}, {"timestamp": 1753889411.1924803, "command": "MOUSE_MOVE -10.0,28.0", "x": -10.0, "y": 28.0, "bullet_index": 3}, {"timestamp": 1753889411.4269829, "command": "MOUSE_MOVE -13.0,29.0", "x": -13.0, "y": 29.0, "bullet_index": 4}], "timing_analysis": {"average_processing_time_ms": 100.85830688476562, "max_processing_time_ms": 101.22489929199219, "min_processing_time_ms": 100.6932258605957, "weapon_delay_ms": 133, "timing_data": [{"bullet": 1, "processing_time_ms": 101.22489929199219, "raw_recoil": {"x": 0.0, "y": -2.257792}, "calculated_pixels": {"x": 0.0, "y": 28.0}, "esp32_success": true, "movement_accurate": false, "accuracy_x": 100, "accuracy_y": 85.71428571428572}, {"bullet": 2, "processing_time_ms": 100.71682929992676, "raw_recoil": {"x": 0.323242, "y": -2.300758}, "calculated_pixels": {"x": -4.0, "y": 28.0}, "esp32_success": true, "movement_accurate": false, "accuracy_x": 75.0, "accuracy_y": 89.28571428571429}, {"bullet": 3, "processing_time_ms": 100.70610046386719, "raw_recoil": {"x": 0.649593, "y": -2.299759}, "calculated_pixels": {"x": -8.0, "y": 28.0}, "esp32_success": true, "movement_accurate": false, "accuracy_x": 87.5, "accuracy_y": 85.71428571428572}, {"bullet": 4, "processing_time_ms": 100.95047950744629, "raw_recoil": {"x": 0.848786, "y": -2.259034}, "calculated_pixels": {"x": -10.0, "y": 28.0}, "esp32_success": true, "movement_accurate": false, "accuracy_x": 80.0, "accuracy_y": 89.28571428571429}, {"bullet": 5, "processing_time_ms": 100.6932258605957, "raw_recoil": {"x": 1.075408, "y": -2.323947}, "calculated_pixels": {"x": -13.0, "y": 29.0}, "esp32_success": true, "movement_accurate": false, "accuracy_x": 92.3076923076923, "accuracy_y": 86.20689655172413}]}, "formula_validation": {"scale": -0.081, "sensitivity": 1.0, "ads_sensitivity": 1.0, "fov": 90, "formula": "-0.03 * (1.0 * 1.0 * 3.0) * (90 / 100)", "result": -0.081}, "movement_validations": [{"expected_x": 0.0, "expected_y": 28.0, "actual_x": 0, "actual_y": 32, "delta_x": 0.0, "delta_y": -4.0, "accuracy_x": 100, "accuracy_y": 85.71428571428572, "is_accurate": false}, {"expected_x": -4.0, "expected_y": 28.0, "actual_x": -3, "actual_y": 31, "delta_x": -1.0, "delta_y": -3.0, "accuracy_x": 75.0, "accuracy_y": 89.28571428571429, "is_accurate": false}, {"expected_x": -8.0, "expected_y": 28.0, "actual_x": -9, "actual_y": 32, "delta_x": 1.0, "delta_y": -4.0, "accuracy_x": 87.5, "accuracy_y": 85.71428571428572, "is_accurate": false}, {"expected_x": -10.0, "expected_y": 28.0, "actual_x": -12, "actual_y": 31, "delta_x": 2.0, "delta_y": -3.0, "accuracy_x": 80.0, "accuracy_y": 89.28571428571429, "is_accurate": false}, {"expected_x": -13.0, "expected_y": 29.0, "actual_x": -14, "actual_y": 33, "delta_x": 1.0, "delta_y": -4.0, "accuracy_x": 92.3076923076923, "accuracy_y": 86.20689655172413, "is_accurate": false}], "mouse_accuracy": {"total_movements": 5, "accurate_movements": 0, "accuracy_rate": 0.0, "average_accuracy_x": 86.96153846153847, "average_accuracy_y": 87.24137931034484, "max_error_x": 2.0, "max_error_y": 4.0, "average_error_x": 1.0, "average_error_y": 3.6}}, {"config_name": "Low_Sens_ADS", "total_tests": 8, "passed": 3, "failed": 5, "warnings": 0, "errors": ["Mouse movement inaccurate for bullet 1: Expected(0.0, 77.0) vs Actual(0, 87)", "Mouse movement inaccurate for bullet 2: Expected(-11.0, 79.0) vs Actual(-13, 89)", "Mouse movement inaccurate for bullet 3: Expected(-22.0, 79.0) vs Actual(-24, 89)", "Mouse movement inaccurate for bullet 4: Expected(-29.0, 77.0) vs Actual(-33, 87)", "Mouse movement inaccurate for bullet 5: Expected(-37.0, 80.0) vs Actual(-42, 90)"], "esp32_commands": [{"timestamp": 1753889411.6640134, "command": "MOUSE_MOVE 0.0,77.0", "x": 0.0, "y": 77.0, "bullet_index": 0}, {"timestamp": 1753889411.8980782, "command": "MOUSE_MOVE -11.0,79.0", "x": -11.0, "y": 79.0, "bullet_index": 1}, {"timestamp": 1753889412.1328, "command": "MOUSE_MOVE -22.0,79.0", "x": -22.0, "y": 79.0, "bullet_index": 2}, {"timestamp": 1753889412.3670535, "command": "MOUSE_MOVE -29.0,77.0", "x": -29.0, "y": 77.0, "bullet_index": 3}, {"timestamp": 1753889412.6009676, "command": "MOUSE_MOVE -37.0,80.0", "x": -37.0, "y": 80.0, "bullet_index": 4}], "timing_analysis": {"average_processing_time_ms": 100.91652870178223, "max_processing_time_ms": 101.25589370727539, "min_processing_time_ms": 100.59762001037598, "weapon_delay_ms": 133, "timing_data": [{"bullet": 1, "processing_time_ms": 100.59762001037598, "raw_recoil": {"x": 0.0, "y": -2.257792}, "calculated_pixels": {"x": 0.0, "y": 77.0}, "esp32_success": true, "movement_accurate": false, "accuracy_x": 100, "accuracy_y": 87.01298701298701}, {"bullet": 2, "processing_time_ms": 101.25589370727539, "raw_recoil": {"x": 0.323242, "y": -2.300758}, "calculated_pixels": {"x": -11.0, "y": 79.0}, "esp32_success": true, "movement_accurate": false, "accuracy_x": 81.81818181818181, "accuracy_y": 87.34177215189874}, {"bullet": 3, "processing_time_ms": 101.20201110839844, "raw_recoil": {"x": 0.649593, "y": -2.299759}, "calculated_pixels": {"x": -22.0, "y": 79.0}, "esp32_success": true, "movement_accurate": false, "accuracy_x": 90.9090909090909, "accuracy_y": 87.34177215189874}, {"bullet": 4, "processing_time_ms": 100.73280334472656, "raw_recoil": {"x": 0.848786, "y": -2.259034}, "calculated_pixels": {"x": -29.0, "y": 77.0}, "esp32_success": true, "movement_accurate": false, "accuracy_x": 86.20689655172413, "accuracy_y": 87.01298701298701}, {"bullet": 5, "processing_time_ms": 100.79431533813477, "raw_recoil": {"x": 1.075408, "y": -2.323947}, "calculated_pixels": {"x": -37.0, "y": 80.0}, "esp32_success": true, "movement_accurate": false, "accuracy_x": 86.48648648648648, "accuracy_y": 87.5}]}, "formula_validation": {"scale": -0.02916, "sensitivity": 0.6, "ads_sensitivity": 0.6, "fov": 90, "formula": "-0.03 * (0.6 * 0.6 * 3.0) * (90 / 100)", "result": -0.02916}, "movement_validations": [{"expected_x": 0.0, "expected_y": 77.0, "actual_x": 0, "actual_y": 87, "delta_x": 0.0, "delta_y": -10.0, "accuracy_x": 100, "accuracy_y": 87.01298701298701, "is_accurate": false}, {"expected_x": -11.0, "expected_y": 79.0, "actual_x": -13, "actual_y": 89, "delta_x": 2.0, "delta_y": -10.0, "accuracy_x": 81.81818181818181, "accuracy_y": 87.34177215189874, "is_accurate": false}, {"expected_x": -22.0, "expected_y": 79.0, "actual_x": -24, "actual_y": 89, "delta_x": 2.0, "delta_y": -10.0, "accuracy_x": 90.9090909090909, "accuracy_y": 87.34177215189874, "is_accurate": false}, {"expected_x": -29.0, "expected_y": 77.0, "actual_x": -33, "actual_y": 87, "delta_x": 4.0, "delta_y": -10.0, "accuracy_x": 86.20689655172413, "accuracy_y": 87.01298701298701, "is_accurate": false}, {"expected_x": -37.0, "expected_y": 80.0, "actual_x": -42, "actual_y": 90, "delta_x": 5.0, "delta_y": -10.0, "accuracy_x": 86.48648648648648, "accuracy_y": 87.5, "is_accurate": false}], "mouse_accuracy": {"total_movements": 5, "accurate_movements": 0, "accuracy_rate": 0.0, "average_accuracy_x": 89.08413115309666, "average_accuracy_y": 87.24190366595431, "max_error_x": 5.0, "max_error_y": 10.0, "average_error_x": 2.6, "average_error_y": 10.0}}, {"config_name": "High_Sens_Hip", "total_tests": 8, "passed": 8, "failed": 0, "warnings": 0, "errors": [], "esp32_commands": [{"timestamp": 1753889412.8386555, "command": "MOUSE_MOVE 0.0,11.0", "x": 0.0, "y": 11.0, "bullet_index": 0}, {"timestamp": 1753889413.0734823, "command": "MOUSE_MOVE -2.0,11.0", "x": -2.0, "y": 11.0, "bullet_index": 1}, {"timestamp": 1753889413.3082187, "command": "MOUSE_MOVE -3.0,11.0", "x": -3.0, "y": 11.0, "bullet_index": 2}, {"timestamp": 1753889413.542177, "command": "MOUSE_MOVE -4.0,11.0", "x": -4.0, "y": 11.0, "bullet_index": 3}, {"timestamp": 1753889413.7765634, "command": "MOUSE_MOVE -5.0,11.0", "x": -5.0, "y": 11.0, "bullet_index": 4}], "timing_analysis": {"average_processing_time_ms": 101.03530883789062, "max_processing_time_ms": 101.30167007446289, "min_processing_time_ms": 100.75831413269043, "weapon_delay_ms": 133, "timing_data": [{"bullet": 1, "processing_time_ms": 101.03940963745117, "raw_recoil": {"x": 0.0, "y": -2.257792}, "calculated_pixels": {"x": 0.0, "y": 11.0}, "esp32_success": true, "movement_accurate": true, "accuracy_x": 100, "accuracy_y": 90.9090909090909}, {"bullet": 2, "processing_time_ms": 101.30167007446289, "raw_recoil": {"x": 0.323242, "y": -2.300758}, "calculated_pixels": {"x": -2.0, "y": 11.0}, "esp32_success": true, "movement_accurate": true, "accuracy_x": 100.0, "accuracy_y": 81.81818181818181}, {"bullet": 3, "processing_time_ms": 100.77977180480957, "raw_recoil": {"x": 0.649593, "y": -2.299759}, "calculated_pixels": {"x": -3.0, "y": 11.0}, "esp32_success": true, "movement_accurate": true, "accuracy_x": 100.0, "accuracy_y": 90.9090909090909}, {"bullet": 4, "processing_time_ms": 101.29737854003906, "raw_recoil": {"x": 0.848786, "y": -2.259034}, "calculated_pixels": {"x": -4.0, "y": 11.0}, "esp32_success": true, "movement_accurate": true, "accuracy_x": 75.0, "accuracy_y": 90.9090909090909}, {"bullet": 5, "processing_time_ms": 100.75831413269043, "raw_recoil": {"x": 1.075408, "y": -2.323947}, "calculated_pixels": {"x": -5.0, "y": 11.0}, "esp32_success": true, "movement_accurate": true, "accuracy_x": 100.0, "accuracy_y": 81.81818181818181}]}, "formula_validation": {"scale": -0.20249999999999999, "sensitivity": 2.5, "ads_sensitivity": 1.0, "fov": 90, "formula": "-0.03 * (2.5 * 1.0 * 3.0) * (90 / 100)", "result": -0.20249999999999999}, "movement_validations": [{"expected_x": 0.0, "expected_y": 11.0, "actual_x": 0, "actual_y": 12, "delta_x": 0.0, "delta_y": -1.0, "accuracy_x": 100, "accuracy_y": 90.9090909090909, "is_accurate": true}, {"expected_x": -2.0, "expected_y": 11.0, "actual_x": -2, "actual_y": 13, "delta_x": 0.0, "delta_y": -2.0, "accuracy_x": 100.0, "accuracy_y": 81.81818181818181, "is_accurate": true}, {"expected_x": -3.0, "expected_y": 11.0, "actual_x": -3, "actual_y": 12, "delta_x": 0.0, "delta_y": -1.0, "accuracy_x": 100.0, "accuracy_y": 90.9090909090909, "is_accurate": true}, {"expected_x": -4.0, "expected_y": 11.0, "actual_x": -5, "actual_y": 12, "delta_x": 1.0, "delta_y": -1.0, "accuracy_x": 75.0, "accuracy_y": 90.9090909090909, "is_accurate": true}, {"expected_x": -5.0, "expected_y": 11.0, "actual_x": -5, "actual_y": 13, "delta_x": 0.0, "delta_y": -2.0, "accuracy_x": 100.0, "accuracy_y": 81.81818181818181, "is_accurate": true}], "mouse_accuracy": {"total_movements": 5, "accurate_movements": 5, "accuracy_rate": 100.0, "average_accuracy_x": 95.0, "average_accuracy_y": 87.27272727272728, "max_error_x": 1.0, "max_error_y": 2.0, "average_error_x": 0.2, "average_error_y": 1.4}}, {"config_name": "Crouch_Standard", "total_tests": 8, "passed": 7, "failed": 1, "warnings": 0, "errors": ["Mouse movement inaccurate for bullet 3: Expected(-5.5, 19.0) vs Actual(-5, 22)"], "esp32_commands": [{"timestamp": 1753889414.0148835, "command": "MOUSE_MOVE 0.0,18.5", "x": 0.0, "y": 18.5, "bullet_index": 0}, {"timestamp": 1753889414.2492545, "command": "MOUSE_MOVE -2.5,19.0", "x": -2.5, "y": 19.0, "bullet_index": 1}, {"timestamp": 1753889414.4836977, "command": "MOUSE_MOVE -5.5,19.0", "x": -5.5, "y": 19.0, "bullet_index": 2}, {"timestamp": 1753889414.7180507, "command": "MOUSE_MOVE -7.0,18.5", "x": -7.0, "y": 18.5, "bullet_index": 3}, {"timestamp": 1753889414.9525936, "command": "MOUSE_MOVE -9.0,19.0", "x": -9.0, "y": 19.0, "bullet_index": 4}], "timing_analysis": {"average_processing_time_ms": 100.94642639160156, "max_processing_time_ms": 101.26090049743652, "min_processing_time_ms": 100.75092315673828, "weapon_delay_ms": 133, "timing_data": [{"bullet": 1, "processing_time_ms": 100.9206771850586, "raw_recoil": {"x": 0.0, "y": -2.257792}, "calculated_pixels": {"x": 0.0, "y": 18.5}, "esp32_success": true, "movement_accurate": true, "accuracy_x": 100, "accuracy_y": 91.89189189189189}, {"bullet": 2, "processing_time_ms": 100.8453369140625, "raw_recoil": {"x": 0.323242, "y": -2.300758}, "calculated_pixels": {"x": -2.5, "y": 19.0}, "esp32_success": true, "movement_accurate": true, "accuracy_x": 80.0, "accuracy_y": 89.47368421052632}, {"bullet": 3, "processing_time_ms": 100.95429420471191, "raw_recoil": {"x": 0.649593, "y": -2.299759}, "calculated_pixels": {"x": -5.5, "y": 19.0}, "esp32_success": true, "movement_accurate": false, "accuracy_x": 90.9090909090909, "accuracy_y": 84.21052631578947}, {"bullet": 4, "processing_time_ms": 101.26090049743652, "raw_recoil": {"x": 0.848786, "y": -2.259034}, "calculated_pixels": {"x": -7.0, "y": 18.5}, "esp32_success": true, "movement_accurate": true, "accuracy_x": 85.71428571428572, "accuracy_y": 91.89189189189189}, {"bullet": 5, "processing_time_ms": 100.75092315673828, "raw_recoil": {"x": 1.075408, "y": -2.323947}, "calculated_pixels": {"x": -9.0, "y": 19.0}, "esp32_success": true, "movement_accurate": true, "accuracy_x": 88.88888888888889, "accuracy_y": 89.47368421052632}]}, "formula_validation": {"scale": -0.060750000000000005, "sensitivity": 1.0, "ads_sensitivity": 0.75, "fov": 90, "formula": "-0.03 * (1.0 * 0.75 * 3.0) * (90 / 100)", "result": -0.060750000000000005}, "movement_validations": [{"expected_x": 0.0, "expected_y": 18.5, "actual_x": 0, "actual_y": 20, "delta_x": 0.0, "delta_y": -1.5, "accuracy_x": 100, "accuracy_y": 91.89189189189189, "is_accurate": true}, {"expected_x": -2.5, "expected_y": 19.0, "actual_x": -3, "actual_y": 21, "delta_x": 0.5, "delta_y": -2.0, "accuracy_x": 80.0, "accuracy_y": 89.47368421052632, "is_accurate": true}, {"expected_x": -5.5, "expected_y": 19.0, "actual_x": -5, "actual_y": 22, "delta_x": -0.5, "delta_y": -3.0, "accuracy_x": 90.9090909090909, "accuracy_y": 84.21052631578947, "is_accurate": false}, {"expected_x": -7.0, "expected_y": 18.5, "actual_x": -8, "actual_y": 20, "delta_x": 1.0, "delta_y": -1.5, "accuracy_x": 85.71428571428572, "accuracy_y": 91.89189189189189, "is_accurate": true}, {"expected_x": -9.0, "expected_y": 19.0, "actual_x": -10, "actual_y": 21, "delta_x": 1.0, "delta_y": -2.0, "accuracy_x": 88.88888888888889, "accuracy_y": 89.47368421052632, "is_accurate": true}], "mouse_accuracy": {"total_movements": 5, "accurate_movements": 4, "accuracy_rate": 80.0, "average_accuracy_x": 89.1024531024531, "average_accuracy_y": 89.38833570412518, "max_error_x": 1.0, "max_error_y": 3.0, "average_error_x": 0.6, "average_error_y": 2.0}}, {"config_name": "Walking_High_FOV", "total_tests": 8, "passed": 7, "failed": 1, "warnings": 0, "errors": ["Mouse movement inaccurate for bullet 2: Expected(-2.3, 17.5) vs Actual(-2, 20)"], "esp32_commands": [{"timestamp": 1753889415.1895065, "command": "MOUSE_MOVE 0.0,17.5", "x": 0.0, "y": 17.549999999999997, "bullet_index": 0}, {"timestamp": 1753889415.4234486, "command": "MOUSE_MOVE -2.3,17.5", "x": -2.34, "y": 17.549999999999997, "bullet_index": 1}, {"timestamp": 1753889415.6583562, "command": "MOUSE_MOVE -4.7,17.5", "x": -4.68, "y": 17.549999999999997, "bullet_index": 2}, {"timestamp": 1753889415.8929079, "command": "MOUSE_MOVE -7.0,17.5", "x": -7.02, "y": 17.549999999999997, "bullet_index": 3}, {"timestamp": 1753889416.127283, "command": "MOUSE_MOVE -8.2,18.7", "x": -8.19, "y": 18.72, "bullet_index": 4}], "timing_analysis": {"average_processing_time_ms": 101.08675956726074, "max_processing_time_ms": 101.34434700012207, "min_processing_time_ms": 100.60906410217285, "weapon_delay_ms": 133, "timing_data": [{"bullet": 1, "processing_time_ms": 100.60906410217285, "raw_recoil": {"x": 0.0, "y": -2.257792}, "calculated_pixels": {"x": 0.0, "y": 17.549999999999997}, "esp32_success": true, "movement_accurate": true, "accuracy_x": 100, "accuracy_y": 91.73789173789172}, {"bullet": 2, "processing_time_ms": 101.1819839477539, "raw_recoil": {"x": 0.323242, "y": -2.300758}, "calculated_pixels": {"x": -2.34, "y": 17.549999999999997}, "esp32_success": true, "movement_accurate": false, "accuracy_x": 85.47008547008548, "accuracy_y": 86.03988603988601}, {"bullet": 3, "processing_time_ms": 101.34434700012207, "raw_recoil": {"x": 0.649593, "y": -2.299759}, "calculated_pixels": {"x": -4.68, "y": 17.549999999999997}, "esp32_success": true, "movement_accurate": true, "accuracy_x": 93.16239316239316, "accuracy_y": 91.73789173789172}, {"bullet": 4, "processing_time_ms": 101.03154182434082, "raw_recoil": {"x": 0.848786, "y": -2.259034}, "calculated_pixels": {"x": -7.02, "y": 17.549999999999997}, "esp32_success": true, "movement_accurate": true, "accuracy_x": 86.03988603988604, "accuracy_y": 91.73789173789172}, {"bullet": 5, "processing_time_ms": 101.26686096191406, "raw_recoil": {"x": 1.075408, "y": -2.323947}, "calculated_pixels": {"x": -8.19, "y": 18.72}, "esp32_success": true, "movement_accurate": true, "accuracy_x": 90.1098901098901, "accuracy_y": 93.16239316239316}]}, "formula_validation": {"scale": -0.14850000000000002, "sensitivity": 1.5, "ads_sensitivity": 1.0, "fov": 110, "formula": "-0.03 * (1.5 * 1.0 * 3.0) * (110 / 100)", "result": -0.14850000000000002}, "movement_validations": [{"expected_x": 0.0, "expected_y": 17.549999999999997, "actual_x": 0, "actual_y": 19, "delta_x": 0.0, "delta_y": -1.4500000000000028, "accuracy_x": 100, "accuracy_y": 91.73789173789172, "is_accurate": true}, {"expected_x": -2.34, "expected_y": 17.549999999999997, "actual_x": -2, "actual_y": 20, "delta_x": -0.33999999999999986, "delta_y": -2.450000000000003, "accuracy_x": 85.47008547008548, "accuracy_y": 86.03988603988601, "is_accurate": false}, {"expected_x": -4.68, "expected_y": 17.549999999999997, "actual_x": -5, "actual_y": 19, "delta_x": 0.3200000000000003, "delta_y": -1.4500000000000028, "accuracy_x": 93.16239316239316, "accuracy_y": 91.73789173789172, "is_accurate": true}, {"expected_x": -7.02, "expected_y": 17.549999999999997, "actual_x": -8, "actual_y": 19, "delta_x": 0.9800000000000004, "delta_y": -1.4500000000000028, "accuracy_x": 86.03988603988604, "accuracy_y": 91.73789173789172, "is_accurate": true}, {"expected_x": -8.19, "expected_y": 18.72, "actual_x": -9, "actual_y": 20, "delta_x": 0.8100000000000005, "delta_y": -1.2800000000000011, "accuracy_x": 90.1098901098901, "accuracy_y": 93.16239316239316, "is_accurate": true}], "mouse_accuracy": {"total_movements": 5, "accurate_movements": 4, "accuracy_rate": 80.0, "average_accuracy_x": 90.95645095645095, "average_accuracy_y": 90.88319088319086, "max_error_x": 0.9800000000000004, "max_error_y": 2.450000000000003, "average_error_x": 0.4900000000000002, "average_error_y": 1.6160000000000025}}, {"config_name": "<PERSON><PERSON>_Walk_ADS", "total_tests": 8, "passed": 5, "failed": 3, "warnings": 0, "errors": ["Mouse movement inaccurate for bullet 1: Expected(0.0, 32.2) vs Actual(0, 36)", "Mouse movement inaccurate for bullet 4: Expected(-12.2, 32.2) vs Actual(-14, 36)", "Mouse movement inaccurate for bullet 5: Expected(-15.5, 33.3) vs Actual(-17, 37)"], "esp32_commands": [{"timestamp": 1753889416.365199, "command": "MOUSE_MOVE 0.0,32.2", "x": 0.0, "y": 32.190000000000005, "bullet_index": 0}, {"timestamp": 1753889416.5995128, "command": "MOUSE_MOVE -4.4,32.7", "x": -4.44, "y": 32.745000000000005, "bullet_index": 1}, {"timestamp": 1753889416.8347955, "command": "MOUSE_MOVE -9.4,32.7", "x": -9.435, "y": 32.745000000000005, "bullet_index": 2}, {"timestamp": 1753889417.0687914, "command": "MOUSE_MOVE -12.2,32.2", "x": -12.21, "y": 32.190000000000005, "bullet_index": 3}, {"timestamp": 1753889417.302903, "command": "MOUSE_MOVE -15.5,33.3", "x": -15.540000000000001, "y": 33.300000000000004, "bullet_index": 4}], "timing_analysis": {"average_processing_time_ms": 100.9450912475586, "max_processing_time_ms": 101.77969932556152, "min_processing_time_ms": 100.51727294921875, "weapon_delay_ms": 133, "timing_data": [{"bullet": 1, "processing_time_ms": 100.72803497314453, "raw_recoil": {"x": 0.0, "y": -2.257792}, "calculated_pixels": {"x": 0.0, "y": 32.190000000000005}, "esp32_success": true, "movement_accurate": false, "accuracy_x": 100, "accuracy_y": 88.16402609506059}, {"bullet": 2, "processing_time_ms": 101.77969932556152, "raw_recoil": {"x": 0.323242, "y": -2.300758}, "calculated_pixels": {"x": -4.44, "y": 32.745000000000005}, "esp32_success": true, "movement_accurate": true, "accuracy_x": 90.09009009009009, "accuracy_y": 90.05955107650024}, {"bullet": 3, "processing_time_ms": 100.51727294921875, "raw_recoil": {"x": 0.649593, "y": -2.299759}, "calculated_pixels": {"x": -9.435, "y": 32.745000000000005}, "esp32_success": true, "movement_accurate": true, "accuracy_x": 94.01165871754108, "accuracy_y": 90.05955107650024}, {"bullet": 4, "processing_time_ms": 100.73637962341309, "raw_recoil": {"x": 0.848786, "y": -2.259034}, "calculated_pixels": {"x": -12.21, "y": 32.190000000000005}, "esp32_success": true, "movement_accurate": false, "accuracy_x": 85.33988533988534, "accuracy_y": 88.16402609506059}, {"bullet": 5, "processing_time_ms": 100.96406936645508, "raw_recoil": {"x": 1.075408, "y": -2.323947}, "calculated_pixels": {"x": -15.540000000000001, "y": 33.300000000000004}, "esp32_success": true, "movement_accurate": false, "accuracy_x": 90.60489060489061, "accuracy_y": 88.8888888888889}]}, "formula_validation": {"scale": -0.03888, "sensitivity": 0.8, "ads_sensitivity": 0.6, "fov": 90, "formula": "-0.03 * (0.8 * 0.6 * 3.0) * (90 / 100)", "result": -0.03888}, "movement_validations": [{"expected_x": 0.0, "expected_y": 32.190000000000005, "actual_x": 0, "actual_y": 36, "delta_x": 0.0, "delta_y": -3.809999999999995, "accuracy_x": 100, "accuracy_y": 88.16402609506059, "is_accurate": false}, {"expected_x": -4.44, "expected_y": 32.745000000000005, "actual_x": -4, "actual_y": 36, "delta_x": -0.4400000000000004, "delta_y": -3.2549999999999955, "accuracy_x": 90.09009009009009, "accuracy_y": 90.05955107650024, "is_accurate": true}, {"expected_x": -9.435, "expected_y": 32.745000000000005, "actual_x": -10, "actual_y": 36, "delta_x": 0.5649999999999995, "delta_y": -3.2549999999999955, "accuracy_x": 94.01165871754108, "accuracy_y": 90.05955107650024, "is_accurate": true}, {"expected_x": -12.21, "expected_y": 32.190000000000005, "actual_x": -14, "actual_y": 36, "delta_x": 1.7899999999999991, "delta_y": -3.809999999999995, "accuracy_x": 85.33988533988534, "accuracy_y": 88.16402609506059, "is_accurate": false}, {"expected_x": -15.540000000000001, "expected_y": 33.300000000000004, "actual_x": -17, "actual_y": 37, "delta_x": 1.459999999999999, "delta_y": -3.6999999999999957, "accuracy_x": 90.60489060489061, "accuracy_y": 88.8888888888889, "is_accurate": false}], "mouse_accuracy": {"total_movements": 5, "accurate_movements": 2, "accuracy_rate": 40.0, "average_accuracy_x": 92.00930495048142, "average_accuracy_y": 89.06720864640212, "max_error_x": 1.7899999999999991, "max_error_y": 3.809999999999995, "average_error_x": 0.8509999999999996, "average_error_y": 3.5659999999999954}}, {"config_name": "Extreme_High_Settings", "total_tests": 8, "passed": 8, "failed": 0, "warnings": 0, "errors": [], "esp32_commands": [{"timestamp": 1753889417.5411975, "command": "MOUSE_MOVE 0.0,6.0", "x": 0.0, "y": 6.0, "bullet_index": 0}, {"timestamp": 1753889417.7752995, "command": "MOUSE_MOVE -1.0,6.0", "x": -1.0, "y": 6.0, "bullet_index": 1}, {"timestamp": 1753889418.0101416, "command": "MOUSE_MOVE -2.0,6.0", "x": -2.0, "y": 6.0, "bullet_index": 2}, {"timestamp": 1753889418.2444568, "command": "MOUSE_MOVE -2.0,6.0", "x": -2.0, "y": 6.0, "bullet_index": 3}, {"timestamp": 1753889418.4786108, "command": "MOUSE_MOVE -3.0,6.0", "x": -3.0, "y": 6.0, "bullet_index": 4}], "timing_analysis": {"average_processing_time_ms": 101.00302696228027, "max_processing_time_ms": 101.20415687561035, "min_processing_time_ms": 100.79002380371094, "weapon_delay_ms": 133, "timing_data": [{"bullet": 1, "processing_time_ms": 100.79002380371094, "raw_recoil": {"x": 0.0, "y": -2.257792}, "calculated_pixels": {"x": 0.0, "y": 6.0}, "esp32_success": true, "movement_accurate": true, "accuracy_x": 100, "accuracy_y": 83.33333333333334}, {"bullet": 2, "processing_time_ms": 100.92926025390625, "raw_recoil": {"x": 0.323242, "y": -2.300758}, "calculated_pixels": {"x": -1.0, "y": 6.0}, "esp32_success": true, "movement_accurate": true, "accuracy_x": 100.0, "accuracy_y": 83.33333333333334}, {"bullet": 3, "processing_time_ms": 101.16052627563477, "raw_recoil": {"x": 0.649593, "y": -2.299759}, "calculated_pixels": {"x": -2.0, "y": 6.0}, "esp32_success": true, "movement_accurate": true, "accuracy_x": 100.0, "accuracy_y": 100.0}, {"bullet": 4, "processing_time_ms": 100.93116760253906, "raw_recoil": {"x": 0.848786, "y": -2.259034}, "calculated_pixels": {"x": -2.0, "y": 6.0}, "esp32_success": true, "movement_accurate": true, "accuracy_x": 100.0, "accuracy_y": 83.33333333333334}, {"bullet": 5, "processing_time_ms": 101.20415687561035, "raw_recoil": {"x": 1.075408, "y": -2.323947}, "calculated_pixels": {"x": -3.0, "y": 6.0}, "esp32_success": true, "movement_accurate": true, "accuracy_x": 66.66666666666667, "accuracy_y": 83.33333333333334}]}, "formula_validation": {"scale": -0.3887999999999999, "sensitivity": 3.0, "ads_sensitivity": 1.2, "fov": 120, "formula": "-0.03 * (3.0 * 1.2 * 3.0) * (120 / 100)", "result": -0.3887999999999999}, "movement_validations": [{"expected_x": 0.0, "expected_y": 6.0, "actual_x": 0, "actual_y": 7, "delta_x": 0.0, "delta_y": -1.0, "accuracy_x": 100, "accuracy_y": 83.33333333333334, "is_accurate": true}, {"expected_x": -1.0, "expected_y": 6.0, "actual_x": -1, "actual_y": 7, "delta_x": 0.0, "delta_y": -1.0, "accuracy_x": 100.0, "accuracy_y": 83.33333333333334, "is_accurate": true}, {"expected_x": -2.0, "expected_y": 6.0, "actual_x": -2, "actual_y": 6, "delta_x": 0.0, "delta_y": 0.0, "accuracy_x": 100.0, "accuracy_y": 100.0, "is_accurate": true}, {"expected_x": -2.0, "expected_y": 6.0, "actual_x": -2, "actual_y": 7, "delta_x": 0.0, "delta_y": -1.0, "accuracy_x": 100.0, "accuracy_y": 83.33333333333334, "is_accurate": true}, {"expected_x": -3.0, "expected_y": 6.0, "actual_x": -4, "actual_y": 7, "delta_x": 1.0, "delta_y": -1.0, "accuracy_x": 66.66666666666667, "accuracy_y": 83.33333333333334, "is_accurate": true}], "mouse_accuracy": {"total_movements": 5, "accurate_movements": 5, "accuracy_rate": 100.0, "average_accuracy_x": 93.33333333333334, "average_accuracy_y": 86.66666666666667, "max_error_x": 1.0, "max_error_y": 1.0, "average_error_x": 0.2, "average_error_y": 0.8}}, {"config_name": "Extreme_Low_Settings", "total_tests": 8, "passed": 3, "failed": 5, "warnings": 0, "errors": ["Mouse movement inaccurate for bullet 1: Expected(0.0, 299.0) vs Actual(0, 48)", "Mouse movement inaccurate for bullet 2: Expected(-43.0, 304.0) vs Actual(-48, 54)", "Mouse movement inaccurate for bullet 3: Expected(-86.0, 304.0) vs Actual(-97, 54)", "Mouse movement inaccurate for bullet 4: Expected(-112.0, 299.0) vs Actual(-126, 49)", "Mouse movement inaccurate for bullet 5: Expected(-142.0, 307.0) vs Actual(127, 57)"], "esp32_commands": [{"timestamp": 1753889418.7168586, "command": "MOUSE_MOVE 0.0,299.0", "x": 0.0, "y": 299.0, "bullet_index": 0}, {"timestamp": 1753889418.9507067, "command": "MOUSE_MOVE -43.0,304.0", "x": -43.0, "y": 304.0, "bullet_index": 1}, {"timestamp": 1753889419.1845675, "command": "MOUSE_MOVE -86.0,304.0", "x": -86.0, "y": 304.0, "bullet_index": 2}, {"timestamp": 1753889419.4189203, "command": "MOUSE_MOVE -112.0,299.0", "x": -112.0, "y": 299.0, "bullet_index": 3}, {"timestamp": 1753889419.6532922, "command": "MOUSE_MOVE -142.0,307.0", "x": -142.0, "y": 307.0, "bullet_index": 4}], "timing_analysis": {"average_processing_time_ms": 100.9246826171875, "max_processing_time_ms": 101.28498077392578, "min_processing_time_ms": 100.65460205078125, "weapon_delay_ms": 133, "timing_data": [{"bullet": 1, "processing_time_ms": 100.65460205078125, "raw_recoil": {"x": 0.0, "y": -2.257792}, "calculated_pixels": {"x": 0.0, "y": 299.0}, "esp32_success": true, "movement_accurate": false, "accuracy_x": 100, "accuracy_y": 16.05351170568562}, {"bullet": 2, "processing_time_ms": 100.67462921142578, "raw_recoil": {"x": 0.323242, "y": -2.300758}, "calculated_pixels": {"x": -43.0, "y": 304.0}, "esp32_success": true, "movement_accurate": false, "accuracy_x": 88.37209302325581, "accuracy_y": 17.763157894736846}, {"bullet": 3, "processing_time_ms": 101.09925270080566, "raw_recoil": {"x": 0.649593, "y": -2.299759}, "calculated_pixels": {"x": -86.0, "y": 304.0}, "esp32_success": true, "movement_accurate": false, "accuracy_x": 87.20930232558139, "accuracy_y": 17.763157894736846}, {"bullet": 4, "processing_time_ms": 101.28498077392578, "raw_recoil": {"x": 0.848786, "y": -2.259034}, "calculated_pixels": {"x": -112.0, "y": 299.0}, "esp32_success": true, "movement_accurate": false, "accuracy_x": 87.5, "accuracy_y": 16.387959866220736}, {"bullet": 5, "processing_time_ms": 100.90994834899902, "raw_recoil": {"x": 1.075408, "y": -2.323947}, "calculated_pixels": {"x": -142.0, "y": 307.0}, "esp32_success": true, "movement_accurate": false, "accuracy_x": -89.43661971830986, "accuracy_y": 18.56677524429967}]}, "formula_validation": {"scale": -0.007559999999999999, "sensitivity": 0.3, "ads_sensitivity": 0.4, "fov": 70, "formula": "-0.03 * (0.3 * 0.4 * 3.0) * (70 / 100)", "result": -0.007559999999999999}, "movement_validations": [{"expected_x": 0.0, "expected_y": 299.0, "actual_x": 0, "actual_y": 48, "delta_x": 0.0, "delta_y": 251.0, "accuracy_x": 100, "accuracy_y": 16.05351170568562, "is_accurate": false}, {"expected_x": -43.0, "expected_y": 304.0, "actual_x": -48, "actual_y": 54, "delta_x": 5.0, "delta_y": 250.0, "accuracy_x": 88.37209302325581, "accuracy_y": 17.763157894736846, "is_accurate": false}, {"expected_x": -86.0, "expected_y": 304.0, "actual_x": -97, "actual_y": 54, "delta_x": 11.0, "delta_y": 250.0, "accuracy_x": 87.20930232558139, "accuracy_y": 17.763157894736846, "is_accurate": false}, {"expected_x": -112.0, "expected_y": 299.0, "actual_x": -126, "actual_y": 49, "delta_x": 14.0, "delta_y": 250.0, "accuracy_x": 87.5, "accuracy_y": 16.387959866220736, "is_accurate": false}, {"expected_x": -142.0, "expected_y": 307.0, "actual_x": 127, "actual_y": 57, "delta_x": -269.0, "delta_y": 250.0, "accuracy_x": -89.43661971830986, "accuracy_y": 18.56677524429967, "is_accurate": false}], "mouse_accuracy": {"total_movements": 5, "accurate_movements": 0, "accuracy_rate": 0.0, "average_accuracy_x": 54.728955126105475, "average_accuracy_y": 17.306912521135946, "max_error_x": 269.0, "max_error_y": 251.0, "average_error_x": 59.8, "average_error_y": 250.2}}], "overall_summary": {"total_tests": 64, "total_passed": 44, "total_failed": 20, "total_warnings": 0, "success_rate": 68.75, "all_errors": ["Mouse movement inaccurate for bullet 1: Expected(0.0, 28.0) vs Actual(0, 32)", "Mouse movement inaccurate for bullet 2: Expected(-4.0, 28.0) vs Actual(-3, 31)", "Mouse movement inaccurate for bullet 3: Expected(-8.0, 28.0) vs Actual(-9, 32)", "Mouse movement inaccurate for bullet 4: Expected(-10.0, 28.0) vs Actual(-12, 31)", "Mouse movement inaccurate for bullet 5: Expected(-13.0, 29.0) vs Actual(-14, 33)", "Mouse movement inaccurate for bullet 1: Expected(0.0, 77.0) vs Actual(0, 87)", "Mouse movement inaccurate for bullet 2: Expected(-11.0, 79.0) vs Actual(-13, 89)", "Mouse movement inaccurate for bullet 3: Expected(-22.0, 79.0) vs Actual(-24, 89)", "Mouse movement inaccurate for bullet 4: Expected(-29.0, 77.0) vs Actual(-33, 87)", "Mouse movement inaccurate for bullet 5: Expected(-37.0, 80.0) vs Actual(-42, 90)", "Mouse movement inaccurate for bullet 3: Expected(-5.5, 19.0) vs Actual(-5, 22)", "Mouse movement inaccurate for bullet 2: Expected(-2.3, 17.5) vs Actual(-2, 20)", "Mouse movement inaccurate for bullet 1: Expected(0.0, 32.2) vs Actual(0, 36)", "Mouse movement inaccurate for bullet 4: Expected(-12.2, 32.2) vs Actual(-14, 36)", "Mouse movement inaccurate for bullet 5: Expected(-15.5, 33.3) vs Actual(-17, 37)", "Mouse movement inaccurate for bullet 1: Expected(0.0, 299.0) vs Actual(0, 48)", "Mouse movement inaccurate for bullet 2: Expected(-43.0, 304.0) vs Actual(-48, 54)", "Mouse movement inaccurate for bullet 3: Expected(-86.0, 304.0) vs Actual(-97, 54)", "Mouse movement inaccurate for bullet 4: Expected(-112.0, 299.0) vs Actual(-126, 49)", "Mouse movement inaccurate for bullet 5: Expected(-142.0, 307.0) vs Actual(127, 57)"]}}