#include "views/MainWindowScripts.h"

namespace octane {
namespace views {

std::string MainWindowScripts::getJavaScriptContent()
{
    std::string script = R"(
<script>
    // Global state
    let currentTab = 'main';
    let recoilEnabled = false;
    let esp32Connected = false;

    // Safe message posting function
    function postMessageToNative(message) {
        try {
            if (window.chrome && window.chrome.webview && window.chrome.webview.postMessage) {
                window.chrome.webview.postMessage(message);
            } else {
                console.log('WebView2 not available, message:', message);
            }
        } catch (error) {
            console.error('Error posting message to native:', error);
        }
    }

    // Tab switching functionality
    function showTab(tabName) {
        try {
        // Hide all tab contents
        const tabContents = document.querySelectorAll('.tab-content');
        tabContents.forEach(content => {
            content.classList.remove('active');
        });

        // Remove active class from all tab buttons
        const tabButtons = document.querySelectorAll('.tab-btn');
        tabButtons.forEach(btn => {
            btn.classList.remove('active');
        });

        // Show selected tab content
        const selectedTab = document.getElementById(tabName + '-tab');
        if (selectedTab) {
            selectedTab.classList.add('active');
        }

        // Add active class to selected tab button
        const selectedButton = document.querySelector(`[data-tab="${tabName}"]`);
        if (selectedButton) {
            selectedButton.classList.add('active');
        }

            currentTab = tabName;

            // Notify C++ about tab change
            postMessageToNative({
                type: 'tabChanged',
                tab: tabName
            });
        } catch (error) {
            console.error('Error in showTab:', error);
        }
    }

    // Weapon selection change
    function onWeaponChange() {
        try {
            const weapon = document.getElementById('primary-weapon').value;
            console.log('Weapon changed to:', weapon);

            postMessageToNative({
                type: 'weaponChanged',
                weapon: weapon
            });
        } catch (error) {
            console.error('Error in onWeaponChange:', error);
        }
    }

    // Sight selection change
    function onSightChange() {
        const sight = document.getElementById('sight').value;
        console.log('Sight changed to:', sight);
        
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'sightChanged',
                sight: sight
            });
        }
    }

    // Muzzle selection change
    function onMuzzleChange() {
        const muzzle = document.getElementById('muzzle').value;
        console.log('Muzzle changed to:', muzzle);
        
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'muzzleChanged',
                muzzle: muzzle
            });
        }
    }

    // Recoil compensation slider
    function onRecoilCompensationChange() {
        const value = document.getElementById('recoil-compensation').value;
        document.getElementById('recoil-compensation-value').textContent = value + '%';
        
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'recoilCompensationChanged',
                value: parseInt(value)
            });
        }
    }

    // Humanization slider
    function onHumanizationChange() {
        const value = document.getElementById('humanization').value;
        document.getElementById('humanization-value').textContent = value + '%';
        
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'humanizationChanged',
                value: parseInt(value)
            });
        }
    }

    // Smoothing slider
    function onSmoothingChange() {
        const value = document.getElementById('smoothing').value;
        document.getElementById('smoothing-value').textContent = value + '%';
        
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'smoothingChanged',
                value: parseInt(value)
            });
        }
    }

    // Hip fire checkbox
    function onHipFireChange() {
        const checked = document.getElementById('hip-fire').checked;
        
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'hipFireChanged',
                enabled: checked
            });
        }
    }

    // Toggle recoil control
    function toggleRecoil() {
        recoilEnabled = !recoilEnabled;
        const button = document.getElementById('recoil-toggle');
        
        if (recoilEnabled) {
            button.textContent = 'ENABLED';
            button.style.background = 'linear-gradient(135deg, #00ff88 0%, #00d4ff 100%)';
        } else {
            button.textContent = 'DISABLED';
            button.style.background = 'linear-gradient(135deg, #00d4ff 0%, #00ff88 100%)';
        }
        
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'recoilToggled',
                enabled: recoilEnabled
            });
        }
    }

    // Sensitivity slider
    function onSensitivityChange() {
        const value = document.getElementById('sensitivity').value;
        document.getElementById('sensitivity-value').textContent = value;
        
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'sensitivityChanged',
                value: parseFloat(value)
            });
        }
    }

    // FOV slider
    function onFovChange() {
        const value = document.getElementById('fov').value;
        document.getElementById('fov-value').textContent = value;
        
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'fovChanged',
                value: parseInt(value)
            });
        }
    }

    // Horizontal multiplier slider
    function onHorizontalMultiplierChange() {
        const value = document.getElementById('horizontal-multiplier').value;
        document.getElementById('horizontal-multiplier-value').textContent = value + '%';
        
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'horizontalMultiplierChanged',
                value: parseInt(value)
            });
        }
    }

    // Vertical multiplier slider
    function onVerticalMultiplierChange() {
        const value = document.getElementById('vertical-multiplier').value;
        document.getElementById('vertical-multiplier-value').textContent = value + '%';
        
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'verticalMultiplierChanged',
                value: parseInt(value)
            });
        }
    }

    // Refresh ports
    function refreshPorts() {
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'refreshPorts'
            });
        }
    }

    // Toggle ESP32 connection
    function toggleConnection() {
        const port = document.getElementById('port-select').value;
        if (!port && !esp32Connected) {
            alert('Please select a port first');
            return;
        }
        
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'toggleConnection',
                port: port,
                connected: esp32Connected
            });
        }
    }

    // Update connection status (called from C++)
    function updateConnectionStatus(connected, port) {
        esp32Connected = connected;
        const statusIndicator = document.getElementById('esp32-connection-status');
        const statusText = document.getElementById('esp32-connection-text');
        const connectBtn = document.getElementById('connect-btn');

        if (connected) {
            statusIndicator.className = 'status-indicator connected';
            statusText.textContent = `Connected to ${port}`;
            connectBtn.textContent = 'Disconnect';
        } else {
            statusIndicator.className = 'status-indicator disconnected';
            statusText.textContent = 'Disconnected';
            connectBtn.textContent = 'Connect';
        }
    }

    // Update available ports (called from C++)
    function updatePorts(ports) {
        const portSelect = document.getElementById('port-select');
        portSelect.innerHTML = '<option value="">Select Port...</option>';
        
        ports.forEach(port => {
            const option = document.createElement('option');
            option.value = port;
            option.textContent = port;
            portSelect.appendChild(option);
        });
    }

    // Initialize when page loads
    // Config Management Functions
    function saveConfig() {
        const configName = document.getElementById('config-name').value.trim();
        if (!configName) {
            alert('Please enter a config name');
            return;
        }

        postMessageToNative({
            type: 'saveConfig',
            configName: configName
        });
    }

    function loadConfig() {
        const configSelect = document.getElementById('config-list');
        const configName = configSelect.value;
        if (!configName) {
            alert('Please select a config to load');
            return;
        }

        postMessageToNative({
            type: 'loadConfig',
            configName: configName
        });
    }

    function deleteConfig() {
        const configSelect = document.getElementById('config-list');
        const configName = configSelect.value;
        if (!configName) {
            alert('Please select a config to delete');
            return;
        }

        if (confirm(`Are you sure you want to delete config "${configName}"?`)) {
            postMessageToNative({
                type: 'deleteConfig',
                configName: configName
            });
        }
    }

    function onConfigSelect() {
        // Config selected, could show preview or enable load button
    }

    function onAutoLoadConfigChange() {
        const autoLoad = document.getElementById('auto-load-config').checked;
        const configSelect = document.getElementById('config-list');
        const configName = configSelect.value;

        postMessageToNative({
            type: 'autoLoadConfigChanged',
            enabled: autoLoad,
            configName: configName
        });
    }

    function refreshConfigList() {
        postMessageToNative({
            type: 'refreshConfigs'
        });
    }
)";

    script += R"(
    // Features Tab Functions
    function onCursorCheckChange() {
        const cursorCheck = document.getElementById('cursor-check');
        if (cursorCheck) {
            console.log('Cursor check changed to:', cursorCheck.checked);
            postMessageToNative({
                type: 'cursorCheckChanged',
                enabled: cursorCheck.checked
            });
        }
    }

    function onSmoothnessChange() {
        const smoothness = document.getElementById('recoil-smoothness');
        const smoothnessValue = document.getElementById('smoothness-value');
        if (smoothness && smoothnessValue) {
            smoothnessValue.textContent = smoothness.value + '%';
            console.log('Recoil smoothness changed to:', smoothness.value);
            postMessageToNative({
                type: 'smoothnessChanged',
                value: parseInt(smoothness.value)
            });
        }
    }

    function onKeybindSoundsChange() {
        const keybindSounds = document.getElementById('keybind-sounds');
        if (keybindSounds) {
            console.log('Keybind sounds changed to:', keybindSounds.checked);
            postMessageToNative({
                type: 'keybindSoundsChanged',
                enabled: keybindSounds.checked
            });
        }
    }

    function onAutoCodeLockChange() {
        const autoCodeLock = document.getElementById('auto-code-lock');
        if (autoCodeLock) {
            console.log('Auto code lock changed to:', autoCodeLock.checked);
            postMessageToNative({
                type: 'autoCodeLockChanged',
                enabled: autoCodeLock.checked
            });
        }
    }

    function onCodeLockDigitsChange() {
        const codeLockDigits = document.getElementById('code-lock-digits');
        if (codeLockDigits) {
            // Ensure only 4 digits
            let value = codeLockDigits.value.replace(/[^0-9]/g, '').substring(0, 4);
            codeLockDigits.value = value;
            console.log('Code lock digits changed to:', value);
            postMessageToNative({
                type: 'codeLockDigitsChanged',
                digits: value
            });
        }
    }

    function onRapidFireChange() {
        const rapidFire = document.getElementById('rapid-fire');
        if (rapidFire) {
            console.log('Rapid fire changed to:', rapidFire.checked);
            postMessageToNative({
                type: 'rapidFireChanged',
                enabled: rapidFire.checked
            });
        }
    }



    function onAntiAfkChange() {
        const antiAfk = document.getElementById('anti-afk');
        if (antiAfk) {
            console.log('Anti AFK changed to:', antiAfk.checked);
            postMessageToNative({
                type: 'antiAfkChanged',
                enabled: antiAfk.checked
            });
        }
    }

    function onAfkIntervalChange() {
        const afkInterval = document.getElementById('afk-interval');
        const afkIntervalValue = document.getElementById('afk-interval-value');
        if (afkInterval && afkIntervalValue) {
            afkIntervalValue.textContent = afkInterval.value + 's';
            console.log('AFK interval changed to:', afkInterval.value);
            postMessageToNative({
                type: 'afkIntervalChanged',
                value: parseInt(afkInterval.value)
            });
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        console.log('Octane Recoil Controller initialized');

        // Request initial data from C++
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'initialized'
            });
        }

        // Load available configs
        refreshConfigList();

        // Setup calibration system
        setupCalibrationSystem();

        // Load loadouts
        loadLoadouts();
    });

    // New loadout functions
    function saveNewLoadout() {
        const name = document.getElementById('loadout-name').value.trim();
        const weapon = document.getElementById('loadout-weapon').value;
        const sight = document.getElementById('loadout-sight').value;
        const muzzle = document.getElementById('loadout-muzzle').value;

        if (!name || !weapon) {
            alert('Please enter a loadout name and select a weapon.');
            return;
        }

        const loadout = {
            id: Date.now(),
            name: name,
            weapon: weapon,
            sight: sight,
            muzzle: muzzle
        };

        // Save to localStorage
        let savedLoadouts = JSON.parse(localStorage.getItem('savedLoadouts') || '[]');
        savedLoadouts.push(loadout);
        localStorage.setItem('savedLoadouts', JSON.stringify(savedLoadouts));

        // Clear form
        document.getElementById('loadout-name').value = '';
        document.getElementById('loadout-weapon').value = '';
        document.getElementById('loadout-sight').value = 'none';
        document.getElementById('loadout-muzzle').value = 'none';

        // Refresh loadout list
        loadLoadouts();

        console.log('Loadout saved:', loadout);
    }

    function loadLoadouts() {
        const savedLoadouts = JSON.parse(localStorage.getItem('savedLoadouts') || '[]');
        const loadoutsList = document.getElementById('saved-loadouts-list');

        if (!loadoutsList) return;

        loadoutsList.innerHTML = '';

        if (savedLoadouts.length === 0) {
            loadoutsList.innerHTML = '<div class="no-loadouts">No saved loadouts</div>';
            return;
        }

        savedLoadouts.forEach(loadout => {
            const loadoutItem = document.createElement('div');
            loadoutItem.className = 'loadout-item';
            loadoutItem.innerHTML = `
                <div class='loadout-info'>
                    <div class='loadout-name'>${loadout.name}</div>
                    <div class='loadout-details'>${loadout.weapon} + ${loadout.sight} + ${loadout.muzzle}</div>
                </div>
                <div class='loadout-actions'>
                    <button onclick='activateLoadout(${loadout.id})' class='primary'>Load</button>
                    <button onclick='deleteLoadout(${loadout.id})' class='secondary'>Delete</button>
                </div>
            `;
            loadoutsList.appendChild(loadoutItem);
        });
    }

    function activateLoadout(loadoutId) {
        const savedLoadouts = JSON.parse(localStorage.getItem('savedLoadouts') || '[]');
        const loadout = savedLoadouts.find(l => l.id === loadoutId);

        if (!loadout) return;

        // Set weapon selection on main tab
        const weaponSelect = document.getElementById('primary-weapon');
        const sightSelect = document.getElementById('sight');
        const muzzleSelect = document.getElementById('muzzle');

        if (weaponSelect) weaponSelect.value = loadout.weapon;
        if (sightSelect) sightSelect.value = loadout.sight;
        if (muzzleSelect) muzzleSelect.value = loadout.muzzle;

        // Trigger change events
        if (weaponSelect) weaponSelect.dispatchEvent(new Event('change'));
        if (sightSelect) sightSelect.dispatchEvent(new Event('change'));
        if (muzzleSelect) muzzleSelect.dispatchEvent(new Event('change'));

        console.log('Loadout activated:', loadout);
    }

    function deleteLoadout(loadoutId) {
        if (!confirm('Are you sure you want to delete this loadout?')) return;

        let savedLoadouts = JSON.parse(localStorage.getItem('savedLoadouts') || '[]');
        savedLoadouts = savedLoadouts.filter(l => l.id !== loadoutId);
        localStorage.setItem('savedLoadouts', JSON.stringify(savedLoadouts));

        loadLoadouts();
        console.log('Loadout deleted:', loadoutId);
    }

    function onGlobalKeybindsChange() {
        const globalKeybinds = document.getElementById('global-keybinds');
        if (globalKeybinds) {
            console.log('Global keybinds changed to:', globalKeybinds.checked);
            // TODO: Implement global keybinds toggle
        }
    }

    function resetKeybinds() {
        // Reset all keybinds to defaults
        const keybindInputs = [
            { id: 'toggle-recoil-key', value: 'F1', action: 'toggleRecoil' },
            { id: 'crouch-key', value: 'Control', action: 'crouchKey' },
            { id: 'emergency-stop-key', value: 'F2', action: 'emergencyStop' },
            { id: 'show-hide-interface-key', value: 'F4', action: 'showHideInterface' },
            { id: 'auto-code-lock-key', value: 'F5', action: 'autoCodeLock' }
        ];

        keybindInputs.forEach(keybind => {
            const input = document.getElementById(keybind.id);
            if (input) {
                input.value = keybind.value;
                // Send to backend
                postMessageToNative({
                    type: 'keybindChanged',
                    action: keybind.action,
                    key: keybind.value
                });
            }
        });

        console.log('Keybinds reset to defaults');
    }

    function captureRecoilKeybind() {
        const keybindBtn = document.getElementById('recoil-keybind-btn');
        if (keybindBtn) {
            keybindBtn.textContent = 'Press key...';
            keybindBtn.style.background = '#00d4ff';
            keybindBtn.style.color = '#000';

            // Add event listener for key capture
            const handleKeyCapture = (event) => {
                event.preventDefault();
                event.stopPropagation();

                let keyName = event.key;

                // Handle special keys
                if (event.key === ' ') keyName = 'Space';
                if (event.key === 'Escape') {
                    keybindBtn.textContent = keybindBtn.getAttribute('data-original') || 'F1';
                    keybindBtn.style.background = '';
                    keybindBtn.style.color = '';
                    return;
                }

                // Format function keys
                if (event.key.startsWith('F') && event.key.length <= 3) {
                    keyName = event.key.toUpperCase();
                }

                keybindBtn.textContent = keyName;
                keybindBtn.style.background = '';
                keybindBtn.style.color = '';

                // Update the keybind input in the keybinds tab as well
                const toggleRecoilKey = document.getElementById('toggle-recoil-key');
                if (toggleRecoilKey) {
                    toggleRecoilKey.value = keyName;
                }

                // Send keybind change to backend
                postMessageToNative({
                    type: 'keybindChanged',
                    action: 'toggleRecoil',
                    key: keyName
                });

                console.log('Recoil keybind set to:', keyName);
            };

            document.addEventListener('keydown', handleKeyCapture, { once: true });

            // Timeout after 10 seconds
            setTimeout(() => {
                if (keybindBtn.textContent === 'Press key...') {
                    keybindBtn.textContent = keybindBtn.getAttribute('data-original') || 'F1';
                    keybindBtn.style.background = '';
                    keybindBtn.style.color = '';
                }
            }, 10000);
        }
    }

</script>
)";

    return script;
}

std::string MainWindowScripts::getCalibrationScripts()
{
    return R"(
<script>
    // Calibration system removed - focusing on smoothing functionality
</script>
)";
}

} // namespace views
} // namespace octane
