@echo off
echo 🚀 Building Template ESP32 Recoil Controller...
echo ========================================

REM Check if Visual Studio is available
where cl >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Setting up Visual Studio environment...
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
)

echo.
echo 📁 Template ESP32 Configuration:
echo   - Sensitivity: 0.6
echo   - ADS Sensitivity: 0.6  
echo   - FOV: 90
echo   - ESP32 Port: COM13
echo   - Debug Panel: Enabled
echo.

echo 🔨 Compiling template_esp32_simple.cpp...

cl /EHsc /std:c++17 template_esp32_simple.cpp /Fe:template_esp32.exe /link user32.lib

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Build successful!
    echo 📁 Output: template_esp32.exe
    echo.
    echo 🎯 Ready to test!
    echo Instructions:
    echo   1. Make sure <PERSON>SP<PERSON> is connected to COM13
    echo   2. Run: template_esp32.exe
    echo   3. Hold LEFT MOUSE BUTTON to activate recoil
    echo   4. Use CTRL for crouch, WASD for movement
    echo.
    echo Press any key to run the template...
    pause >nul
    template_esp32.exe
) else (
    echo.
    echo ❌ Build failed!
    echo Check the error messages above.
    pause
)
