#include "views/MainWindowHtml.h"
#include "views/MainWindowStyles.h"
#include "views/MainWindowStructure.h"
#include "views/MainWindowScripts.h"

namespace octane {
namespace views {

std::string MainWindowHtml::getHtmlContent()
{
    // Build HTML in parts to avoid string size limits
    std::string html = R"(<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Octane Recoil Scripts v4.2.1</title>
    <style>)";
    
    html += MainWindowStyles::getCssContent();
    html += R"(
    </style>
</head>)";
    
    html += MainWindowStructure::getHtmlStructure();
    html += getJavaScriptPart();
    html += getTabFunctions();
    html += getMainTabFunctions();
    html += getInitFunction();
    html += MainWindowScripts::getCalibrationScripts();
    html += R"(
</html>)";

    return html;
}

std::string MainWindowHtml::getJavaScriptPart()
{
    return R"(
<script>
    // Enhanced error handling wrapper
    window.addEventListener('error', function(e) {
        console.error('JavaScript error:', e.error);
        return false;
    });
    
    // Safe message posting function
    function postMessageToNative(message) {
        try {
            if (window.chrome && window.chrome.webview && window.chrome.webview.postMessage) {
                const jsonMessage = JSON.stringify(message);
                window.chrome.webview.postMessage(jsonMessage);
            } else {
                console.error('WebView2 not available');
            }
        } catch (error) {
            console.error('Error posting message to native:', error);
        }
    }
</script>
)";
}

std::string MainWindowHtml::getTabFunctions()
{
    return R"(
<script>
    // Tab functions
    function showTab(tabName) {
        try {
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                if (content) content.classList.remove('active');
            });

            const tabButtons = document.querySelectorAll('.tab-btn');
            tabButtons.forEach(btn => {
                if (btn) btn.classList.remove('active');
            });

            const selectedTab = document.getElementById(tabName + '-tab');
            if (selectedTab) {
                selectedTab.classList.add('active');
            }

            const selectedButton = document.querySelector(`[data-tab="${tabName}"]`);
            if (selectedButton) {
                selectedButton.classList.add('active');
            }

            postMessageToNative({type: 'tabChanged', tab: tabName});
        } catch (error) {
            console.error('Error in showTab:', error);
        }
    }
    
    // Basic functions
    function onWeaponChange() {
        postMessageToNative({type: 'weaponChanged', weapon: document.getElementById('primary-weapon').value});
    }
    
    function onSightChange() {
        postMessageToNative({type: 'sightChanged', sight: document.getElementById('sight').value});
    }
    
    function onMuzzleChange() {
        postMessageToNative({type: 'muzzleChanged', muzzle: document.getElementById('muzzle').value});
    }
    
    // Recoil state
    let recoilEnabled = false;
    let humanizationEnabled = false;

    // Weapon keybind functions
    let currentWeaponKeybinds = {
        'ak': '1',
        'lr300': '2',
        'mp5': '3',
        'thompson': '4',
        'm249': '5',
        'smg': '6'
    };

    function onWeaponKeybindSelect() {
        const dropdown = document.getElementById('weapon-keybind-select');
        const input = document.getElementById('weapon-keybind-input');
        if (dropdown && input) {
            const selectedWeapon = dropdown.value;
            input.value = currentWeaponKeybinds[selectedWeapon] || '';
        }
    }

    function getKeyName(event) {
        let keyName = event.key;

        // Handle special keys
        if (event.key === ' ') keyName = 'Space';
        if (event.key === 'Escape') return 'Escape';

        // Format function keys and special keys
        if (event.key.startsWith('F') && event.key.length <= 3) {
            keyName = event.key.toUpperCase();
        }

        return keyName;
    }

    function captureWeaponKeybind() {
        const dropdown = document.getElementById('weapon-keybind-select');
        const input = document.getElementById('weapon-keybind-input');

        if (!dropdown || !input) return;

        const selectedWeapon = dropdown.value;
        input.value = 'Press any key...';
        input.style.background = 'rgba(255, 255, 0, 0.2)';
        input.style.color = '#000';

        document.addEventListener('keydown', function(event) {
            event.preventDefault();
            const keyName = getKeyName(event);

            if (keyName === 'Escape') {
                input.value = currentWeaponKeybinds[selectedWeapon] || '';
                input.style.background = '';
                input.style.color = '';
                return;
            }

            // Update the keybind
            currentWeaponKeybinds[selectedWeapon] = keyName;
            input.value = keyName;
            input.style.background = '';
            input.style.color = '';

            // Send to backend
            postMessageToNative({
                type: 'weaponKeybindChanged',
                weapon: selectedWeapon,
                key: keyName
            });
        }, { once: true });
    }

    function onCursorCheckChange() {
        const checkbox = document.getElementById('cursor-check');
        if (checkbox) {
            const enabled = checkbox.checked;
            console.log('Cursor check changed to:', enabled);
            postMessageToNative({
                type: 'cursorCheckChanged',
                enabled: enabled
            });
        }
    }

    function toggleRecoil() {
        recoilEnabled = !recoilEnabled;
        updateRecoilButton();
        postMessageToNative({type: 'recoilToggled', enabled: recoilEnabled});
    }

    function toggleHumanization() {
        humanizationEnabled = !humanizationEnabled;
        updateHumanizationButton();
        postMessageToNative({type: 'humanizationToggled', enabled: humanizationEnabled});
    }

    function updateHumanizationButton() {
        const button = document.getElementById('humanization-toggle');
        if (button) {
            button.textContent = humanizationEnabled ? 'ENABLED' : 'DISABLED';
            button.className = humanizationEnabled ? 'humanization-toggle enabled' : 'humanization-toggle';
        }
    }

    function captureHumanizationKeybind() {
        const button = document.getElementById('humanization-keybind-btn');
        if (button) {
            button.textContent = 'Press key...';
            button.style.background = '#00d4ff';
            button.style.color = '#000';
            postMessageToNative({type: 'captureHumanizationKeybind'});
        }
    }

    function onHumanizationRandomizationChange() {
        const slider = document.getElementById('humanization-randomization');
        const input = document.getElementById('humanization-randomization-input');
        if (slider && input) {
            input.value = slider.value;
            postMessageToNative({
                type: 'humanizationRandomizationChanged',
                value: parseInt(slider.value)
            });
        }
    }

    function onHumanizationRandomizationInputChange() {
        const slider = document.getElementById('humanization-randomization');
        const input = document.getElementById('humanization-randomization-input');
        if (slider && input) {
            slider.value = input.value;
            postMessageToNative({
                type: 'humanizationRandomizationChanged',
                value: parseInt(input.value)
            });
        }
    }

    function onHumanizationLevelChange() {
        const slider = document.getElementById('humanization-level');
        const input = document.getElementById('humanization-level-input');
        if (slider && input) {
            input.value = slider.value;
            postMessageToNative({
                type: 'humanizationLevelChanged',
                value: parseInt(slider.value)
            });
        }
    }

    function onHumanizationLevelInputChange() {
        const slider = document.getElementById('humanization-level');
        const input = document.getElementById('humanization-level-input');
        if (slider && input) {
            slider.value = input.value;
            postMessageToNative({
                type: 'humanizationLevelChanged',
                value: parseInt(input.value)
            });
        }
    }

    function updateRecoilButton() {
        const button = document.getElementById('recoil-toggle');
        if (button) {
            if (recoilEnabled) {
                button.textContent = 'ENABLED';
                button.style.background = 'linear-gradient(135deg, #00ff88 0%, #00d4ff 100%)';
                button.style.color = '#000000';
            } else {
                button.textContent = 'DISABLED';
                button.style.background = 'linear-gradient(135deg, rgba(255, 68, 68, 0.8) 0%, rgba(255, 68, 68, 0.6) 100%)';
                button.style.color = '#ffffff';
            }
        }
    }
    
    function onSensitivityChange() {
        const value = document.getElementById('sensitivity').value;
        document.getElementById('sensitivity-input').value = value;
        postMessageToNative({type: 'sensitivityChanged', value: parseFloat(value)});
    }
    
    function onSensitivityInputChange() {
        const value = document.getElementById('sensitivity-input').value;
        document.getElementById('sensitivity').value = value;
        postMessageToNative({type: 'sensitivityChanged', value: parseFloat(value)});
    }
    
    function onAdsSensitivityChange() {
        const value = document.getElementById('ads-sensitivity').value;
        document.getElementById('ads-sensitivity-input').value = value;
        postMessageToNative({type: 'adsSensitivityChanged', value: parseFloat(value)});
    }
    
    function onAdsSensitivityInputChange() {
        const value = document.getElementById('ads-sensitivity-input').value;
        document.getElementById('ads-sensitivity').value = value;
        postMessageToNative({type: 'adsSensitivityChanged', value: parseFloat(value)});
    }
    
    function onFovChange() {
        const value = document.getElementById('fov').value;
        document.getElementById('fov-input').value = value;
        postMessageToNative({type: 'fovChanged', value: parseInt(value)});
    }
    
    function onFovInputChange() {
        const value = document.getElementById('fov-input').value;
        document.getElementById('fov').value = value;
        postMessageToNative({type: 'fovChanged', value: parseInt(value)});
    }
</script>
)";
}

std::string MainWindowHtml::getMainTabFunctions()
{
    return R"(
<script>
    // New main tab functions
    function captureRecoilKeybind() {
        const btn = document.getElementById('recoil-keybind-btn');
        btn.textContent = 'Press key...';
        btn.style.background = 'rgba(255, 255, 0, 0.2)';
        
        document.addEventListener('keydown', function(event) {
            event.preventDefault();
            const key = event.key.toUpperCase();
            btn.textContent = key;
            btn.style.background = 'rgba(0, 212, 255, 0.1)';
            
            // Update the keybinds tab as well
            const keybindInput = document.getElementById('toggle-recoil-key');
            if (keybindInput) {
                keybindInput.value = key;
            }
            
            postMessageToNative({type: 'keybindChanged', action: 'toggleRecoil', key: key});
        }, { once: true });
    }

    function onCompensateHipfireChange() {
        const enabled = document.getElementById('compensate-hipfire').checked;
        postMessageToNative({type: 'compensateHipfireChanged', enabled: enabled});
    }

    function onHipfireModeChange() {
        const mode = document.getElementById('hipfire-mode').value;
        postMessageToNative({type: 'hipfireModeChanged', mode: mode});
    }

    // Main tab settings functions (sync with settings tab)
    function onMainSensitivityChange() {
        const value = parseFloat(document.getElementById('main-sensitivity').value);
        document.getElementById('sensitivity').value = value;
        document.getElementById('sensitivity-input').value = value;
        postMessageToNative({type: 'sensitivityChanged', value: value});
    }

    function onMainAdsSensitivityChange() {
        const value = parseFloat(document.getElementById('main-ads-sensitivity').value);
        document.getElementById('ads-sensitivity').value = value;
        document.getElementById('ads-sensitivity-input').value = value;
        postMessageToNative({type: 'adsSensitivityChanged', value: value});
    }

    function onMainFovChange() {
        const value = parseInt(document.getElementById('main-fov').value);
        document.getElementById('fov').value = value;
        document.getElementById('fov-input').value = value;
        postMessageToNative({type: 'fovChanged', value: value});
    }

    // Randomization functions
    function onHorizontalRandomizationChange() {
        const value = document.getElementById('horizontal-randomization').value;
        document.getElementById('horizontal-randomization-input').value = value;
        postMessageToNative({type: 'horizontalRandomizationChanged', value: parseInt(value)});
    }

    function onHorizontalRandomizationInputChange() {
        const value = document.getElementById('horizontal-randomization-input').value;
        document.getElementById('horizontal-randomization').value = value;
        postMessageToNative({type: 'horizontalRandomizationChanged', value: parseInt(value)});
    }

    function onVerticalRandomizationChange() {
        const value = document.getElementById('vertical-randomization').value;
        document.getElementById('vertical-randomization-input').value = value;
        postMessageToNative({type: 'verticalRandomizationChanged', value: parseInt(value)});
    }

    function onVerticalRandomizationInputChange() {
        const value = document.getElementById('vertical-randomization-input').value;
        document.getElementById('vertical-randomization').value = value;
        postMessageToNative({type: 'verticalRandomizationChanged', value: parseInt(value)});
    }
</script>
)";
}

std::string MainWindowHtml::getBasicFunctions()
{
    return "";
}

std::string MainWindowHtml::getInitFunction()
{
    return R"(
<script>
    // License functions
    let authenticationStep = 0;
    const authSteps = [
        'Initializing...',
        'Checking license...',
        'Validating credentials...',
        'Loading configuration...',
        'Ready!'
    ];

    // Simple License System - Brand New Implementation
    function initializeLicenseSystem() {
        console.log('Initializing new license system...');

        // Show the license modal immediately (skip localStorage for WebView2 compatibility)
        const modal = document.getElementById('license-modal');
        const input = document.getElementById('license-key-input');

        if (modal && input) {
            modal.style.display = 'flex';
            setTimeout(() => input.focus(), 300);
            console.log('License modal displayed and ready for input');
        }
    }

    // New Simple License Validation Function
    function validateLicenseKey() {
        console.log('validateLicenseKey() called');

        const input = document.getElementById('license-key-input');
        const errorDiv = document.getElementById('license-error');
        const validateBtn = document.getElementById('validate-btn');

        if (!input || !errorDiv || !validateBtn) {
            console.error('License form elements not found!');
            return;
        }

        const licenseKey = input.value.trim().toUpperCase();
        console.log('License key entered:', licenseKey);

        // Clear previous errors
        errorDiv.style.display = 'none';
        errorDiv.textContent = '';

        // Basic validation
        if (!licenseKey) {
            showLicenseError('Please enter a license key');
            return;
        }

        if (licenseKey.length < 3) {
            showLicenseError('License key is too short');
            return;
        }

        // Show processing state
        validateBtn.textContent = 'Validating...';
        validateBtn.disabled = true;
        input.disabled = true;

        // Send to C++ backend for validation (skip localStorage for WebView2 compatibility)
        console.log('Sending license validation to backend...');
        postMessageToNative({
            type: 'validateLicense',
            key: licenseKey,
            timestamp: Date.now()
        });
    }

    function showLicenseError(message) {
        const errorDiv = document.getElementById('license-error');
        if (errorDiv) {
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            console.log('❌ License error:', message);
        }
    }

    function exitApp() {
        console.log('🚪 Exit application requested');
        postMessageToNative({type: 'exitApplication'});
    }

    function changeLicense() {
        // Skip localStorage operations for WebView2 compatibility
        initializeLicenseSystem();
    }

    function updateLicenseInfo(licenseInfo) {
        if (licenseInfo.isValid) {
            document.getElementById('main-container').style.display = 'block';
            document.getElementById('license-modal').style.display = 'none';
            console.log('License validated successfully');
        } else {
            showLicenseError(licenseInfo.error || 'Invalid license key');

            // Reset button state
            const validateBtn = document.getElementById('validate-btn');
            const input = document.getElementById('license-key-input');
            if (validateBtn && input) {
                validateBtn.textContent = 'Activate License';
                validateBtn.disabled = false;
                input.disabled = false;
            }
        }
    }

    // ESP32 functions
    function refreshPorts() {
        postMessageToNative({type: 'refreshPorts'});
    }

    function populateComPorts(ports) {
        try {
            const dropdown = document.getElementById('esp32-port-dropdown');
            if (!dropdown) return;

            // Clear existing options except the first one
            dropdown.innerHTML = '<option value="">Select COM port...</option>';

            // Add discovered ports
            ports.forEach(port => {
                const option = document.createElement('option');
                option.value = port;
                option.textContent = port;
                dropdown.appendChild(option);
            });

            console.log('COM ports populated:', ports);
        } catch (error) {
            console.error('Error populating COM ports:', error);
        }
    }

    // Alias function for backend compatibility
    function updateComPorts(ports) {
        console.log('updateComPorts called with:', ports);
        populateComPorts(ports);
    }

    function updateConnectionStatus(status, isConnected) {
        const statusElement = document.getElementById('esp32-connection-status') || document.getElementById('connection-text');
        const connectBtn = document.getElementById('esp32-connect-btn');
        const statusDot = document.getElementById('connection-dot');

        if (statusElement) {
            statusElement.textContent = status;
            statusElement.style.color = isConnected ? '#2ed573' : '#ff4757';
        }

        if (statusDot) {
            if (isConnected) {
                statusDot.classList.add('connected');
            } else {
                statusDot.classList.remove('connected');
            }
        }

        if (connectBtn) {
            connectBtn.textContent = isConnected ? 'Disconnect' : 'Connect';
            if (isConnected) {
                connectBtn.classList.add('disconnect');
            } else {
                connectBtn.classList.remove('disconnect');
            }
        }

        console.log(isConnected ? 'ESP32 Connected:' : 'ESP32 Disconnected:', status);
    }

    function toggleESP32Connection() {
        const portDropdown = document.getElementById('esp32-port-dropdown');
        const selectedPort = portDropdown.value;
        const connectBtn = document.getElementById('esp32-connect-btn');
        const isConnected = connectBtn.textContent === 'Disconnect';

        if (isConnected) {
            postMessageToNative({type: 'disconnectESP32'});
        } else {
            if (!selectedPort) {
                alert('Please select a COM port first');
                return;
            }
            postMessageToNative({type: 'connectESP32', port: selectedPort});
        }
    }

    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Octane Recoil Controller initialized');
        postMessageToNative({type: 'initialized'});

        // Add message listener for backend responses
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.addEventListener('message', function(event) {
                try {
                    const data = JSON.parse(event.data);
                    console.log('Received message from backend:', data);

                    if (data.type === 'esp32Connected') {
                        if (data.success) {
                            updateConnectionStatus('Connected to ' + data.port, true);
                        } else {
                            updateConnectionStatus('Connection failed: ' + data.message, false);
                        }
                    } else if (data.type === 'esp32Disconnected') {
                        updateConnectionStatus('Disconnected', false);
                    }
                } catch (error) {
                    console.error('Error parsing backend message:', error);
                }
            });
        }

        // Initialize recoil button state
        updateRecoilButton();

        // Start new license system
        initializeLicenseSystem();

        // Auto-discover COM ports on startup
        setTimeout(() => {
            refreshPorts();
        }, 1000);
    });

    // Keybind capture functionality
    let isCapturingKey = false;
    let currentKeyInput = null;

    function captureKey(inputElement, action) {
        if (isCapturingKey) return;

        isCapturingKey = true;
        currentKeyInput = inputElement;
        inputElement.value = 'Press any key...';
        inputElement.style.background = '#00d4ff';
        inputElement.style.color = '#000';

        // Add event listener for key capture
        document.addEventListener('keydown', handleKeyCapture, { once: true });

        // Timeout after 10 seconds
        setTimeout(() => {
            if (isCapturingKey) {
                cancelKeyCapture();
            }
        }, 10000);
    }

    function handleKeyCapture(event) {
        event.preventDefault();
        event.stopPropagation();

        if (!currentKeyInput) return;

        let keyName = event.key;

        // Handle special keys
        if (event.key === ' ') keyName = 'Space';
        if (event.key === 'Escape') {
            cancelKeyCapture();
            return;
        }

        // Format function keys and special keys
        if (event.key.startsWith('F') && event.key.length <= 3) {
            keyName = event.key.toUpperCase();
        }

        currentKeyInput.value = keyName;
        currentKeyInput.style.background = '';
        currentKeyInput.style.color = '';

        // Get the action from the onclick attribute
        const onclickAttr = currentKeyInput.getAttribute('onclick');
        const actionMatch = onclickAttr.match(/captureKey\\(this,\\s*\"([^\"]+)\"\\)/);
        const action = actionMatch ? actionMatch[1] : 'unknown';

        // Send keybind change to backend
        postMessageToNative({
            type: 'keybindChanged',
            action: action,
            key: keyName
        });

        isCapturingKey = false;
        currentKeyInput = null;

        console.log('Key captured:', keyName, 'for action:', action);
    }

    function cancelKeyCapture() {
        if (currentKeyInput) {
            currentKeyInput.value = currentKeyInput.getAttribute('data-original') || '';
            currentKeyInput.style.background = '';
            currentKeyInput.style.color = '';
        }
        isCapturingKey = false;
        currentKeyInput = null;
    }

    function clearKey(inputId) {
        const input = document.getElementById(inputId);
        if (input) {
            input.value = '';

            // Get the action from the onclick attribute
            const onclickAttr = input.getAttribute('onclick');
            const actionMatch = onclickAttr.match(/captureKey\\(this,\\s*\"([^\"]+)\"\\)/);
            const action = actionMatch ? actionMatch[1] : 'unknown';

            // Send clear keybind to backend
            postMessageToNative({
                type: 'keybindChanged',
                action: action,
                key: ''
            });
        }
    }



</script>
)";
}

} // namespace views
} // namespace octane
