@echo off
echo ========================================
echo TEMPLATE WITH ESP32 BUILD SCRIPT
echo Sensitivity: 0.6, ADS: 0.6, ESP32: Enabled
echo ========================================

echo.
echo 🔧 Building template with ESP32 support...
echo.

REM Check if Visual Studio is available
where cl >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Setting up Visual Studio environment...
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
)

echo.
echo 📁 Template files:
echo   - Sensitivity: 0.6 (updated)
echo   - ADS Sensitivity: 0.6 (updated)  
echo   - ESP32: Enabled
echo   - Port: COM13
echo.

REM Create build directory
if not exist "build" mkdir build
cd build

echo 🚀 Compiling template with ESP32...

REM Compile all source files
cl /EHsc /std:c++17 /I.. ^
   ..\core\core.cpp ^
   ..\core\esp32\esp32.cpp ^
   ..\core\mouse\mouse.cpp ^
   ..\core\movement\movement.cpp ^
   ..\core\timer\timer.cpp ^
   ..\main.cpp ^
   /Fe:template_esp32.exe ^
   /link user32.lib

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ BUILD SUCCESSFUL!
    echo.
    echo 📊 TEMPLATE CONFIGURATION:
    echo   ✅ Sensitivity: 0.6
    echo   ✅ ADS Sensitivity: 0.6
    echo   ✅ ESP32 Hardware Control: Enabled
    echo   ✅ Port: COM13
    echo   ✅ Exact AK Pattern: 38 bullets
    echo.
    echo 🎯 TEMPLATE FEATURES:
    echo   - Same formula as C++ app
    echo   - Same movement detection
    echo   - Same crouch detection
    echo   - ESP32 hardware control
    echo.
    echo 📁 Executable: build\template_esp32.exe
    echo.
    echo 🔌 To test:
    echo   1. Connect ESP32 to COM13
    echo   2. Run: build\template_esp32.exe
    echo   3. Compare with C++ application
    echo.
) else (
    echo.
    echo ❌ BUILD FAILED!
    echo Check the error messages above.
    echo.
)

cd ..
pause
