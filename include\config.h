#ifndef CONFIG_H
#define CONFIG_H

// ===== FIRMWARE INFORMATION =====
#define FIRMWARE_VERSION "2.1.0"
#define HARDWARE_VERSION "ESP32-S2"

// ===== HARDWARE CONFIGURATION =====
#define SERIAL_BAUD 115200
#define LED_PIN 15

// ===== SYSTEM CONFIGURATION - OPTIMIZED FOR RECOIL =====
#define QUEUE_SIZE 128              // Increased for burst recoil commands
#define COMMAND_BUFFER_SIZE 256     // Reduced for faster parsing
#define JSON_BUFFER_SIZE 512        // Reduced - we don't use complex JSON
#define MAX_COMMAND_LENGTH 64       // Fast command parsing

// ===== TIMING CONFIGURATION - OPTIMIZED FOR RECOIL =====
#define HEARTBEAT_INTERVAL 10000  // 10 seconds (reduced frequency)
#define BOOT_STATUS_INTERVAL 2000  // 2 seconds (faster boot)
#define BOOT_STATUS_COUNT 3  // Send only 3 boot status messages (faster boot)

// ===== DEBUG CONFIGURATION =====
// Comment out for maximum speed recoil control (production mode)
// #define DEBUG_MOUSE_MOVES  // Enable mouse move debug output
// #define DEBUG_COMMANDS     // Enable command debug output

// ===== COMMAND TYPES =====
enum CommandType {
    CMD_MOUSE_MOVE = 0,
    CMD_CLICK_DOWN = 1,
    CMD_CLICK_UP = 2,
    CMD_STATUS = 3,
    CMD_PING = 4
};

// ===== LED STATE DEFINITIONS =====
enum LEDState {
    LED_BOOT = 0,           // Fast blink during boot
    LED_WAITING = 1,        // Slow pulse waiting for connection
    LED_CONNECTED = 2,      // Solid on when connected
    LED_ACTIVE = 3,         // Quick double blink during activity
    LED_ERROR = 4,          // Fast triple blink for errors
    LED_STEALTH = 5,        // Very dim pulse in stealth mode
    LED_UPDATING = 6,       // Rapid blink during firmware update
    LED_BINDING = 7,        // Alternating pattern during hardware binding
    LED_AUTHENTICATED = 8,  // Steady glow when authenticated
    LED_COMMAND_RECEIVED = 9 // Brief flash when command received
};

// ===== COMMAND STRUCTURE =====
struct HIDCommand {
    CommandType type;
    int16_t x;
    int16_t y;
    uint8_t button;
    uint32_t timestamp;
    bool processed;
};

#endif // CONFIG_H
